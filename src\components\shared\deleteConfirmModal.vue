<template>
  <div>
    <p>
      Are you sure you want to delete this <b>{{ toBeDeleted }}</b
      >? <br /><b>This action cannot be undone.</b>
    </p>
    <b-button class="primaryBg float-right m-2" @click="onCancel">No</b-button>
    <b-button class="primsaryBg float-right m-2" @click="onConfirm">Yes</b-button>
  </div>
</template>

<script>
export default {
  name: 'deleteConfirmModal',
  props: {
    toBeDeleted: String,
    id: Number,
    deleteAPI: String,
  },
  methods: {
    onConfirm() {
      this.$store
        .dispatch(this.deleteAPI, this.id)
        .then(response => {
          if (response.message == 'Drawtool Deleted') {
            this.$root.$emit('aoiDeleted', `${this.toBeDeleted}-${this.id}`);
          }
          this.$root.$emit('showToast', {
            title: response.message,
            message: `${this.toBeDeleted} deleted successfully`,
            variant: 'success',
          });
        })
        .catch(error => {
          this.$root.$emit('showToast', {
            title: error.message,
            message: 'There has been a problem in removing data',
            variant: 'danger',
          });
        });
      this.onCancel();
    },
    onCancel() {
      this.$bvModal.hide('deleteConfirmModal');
    },
  },
};
</script>

<style scoped></style>
