import keycloakService from '@/services/keycloakService';
import { createRouter, createWebHistory } from 'vue-router';

const home = () => import('@/components/modules/home/<USER>');
const mapPage = () => import('@/components/modules/mapPage.vue');
const analyticsPage = () => import('@/components/modules/analyticsPage.vue');
const manageUsers = () => import('@/components/modules/user/manageUsers.vue');
const userProfile = () => import('@/components/modules/user/userProfile.vue');
const login = () => import('@/components/modules/user/login.vue');
const register = () => import('@/components/modules/user/register.vue');
const compareMaps = () => import('@/components/modules/airmap/compareMaps/compareMaps.vue');
const accountRecovery = () => import('@/components/modules/user/accountRecovery.vue');
const recovery = () => import('@/components/modules/user/recovery.vue');
const notFound = () => import('@/components/shared/notFound.vue');

const router = createRouter({
  history: createWebHistory(process.env.NODE_ENV === 'production' ? '/' : '/'),
  routes: [
    {
      path: '/',
      redirect: '/home',
    },
    {
      path: '/home',
      name: 'home',
      component: home,
    },
    {
      path: '/analytics',
      name: 'analytics',
      component: analyticsPage,
    },
    {
      path: '/user-profile',
      name: 'user-profile',
      component: userProfile,
    },
    {
      path: '/manage-users',
      name: 'manage-users',
      component: manageUsers,
    },
    {
      path: '/map/:projectId',
      name: 'map',
      component: mapPage,
    },
    {
      path: '/compare',
      name: 'compare',
      component: compareMaps,
    },
    {
      path: '/login',
      name: 'login',
      component: login,
      meta: {
        public: true, // Allow access to even if not logged in
        onlyWhenLoggedOut: true,
      },
    },
    {
      path: '/register',
      name: 'register',
      component: register,
      meta: {
        public: true, // Allow access to even if not logged in
        onlyWhenLoggedOut: true,
      },
    },
    {
      path: '/recovery',
      name: 'accountRecovery',
      component: accountRecovery,
      meta: {
        public: true, // Allow access to even if not logged in
        onlyWhenLoggedOut: true,
      },
    },
    {
      path: '/recovery/:uid/:token',
      name: 'recovery',
      component: recovery,
      props: true,
      meta: {
        public: true, // Allow access to even if not logged in
        onlyWhenLoggedOut: true,
      },
    },
    { path: '/:pathMatch(.*)*', component: notFound },
  ],
});

router.beforeEach((to, from, next) => {
  const isPublic = to.matched.some(record => record.meta.public);
  const onlyWhenLoggedOut = to.matched.some(record => record.meta.onlyWhenLoggedOut);

  const loggedIn = keycloakService.isLoggedIn();
  if (!isPublic && !loggedIn) return next({ path: '/login' });

  // Do not allow user to visit login page or register page if they are logged in
  if (loggedIn && onlyWhenLoggedOut) return next('/');
  next();
});

router.afterEach(() => {
  if (window.location.hash) {
    const cleanPath = window.location.pathname + window.location.search;
    history.replaceState(null, null, cleanPath);
  }
});
export default router;
