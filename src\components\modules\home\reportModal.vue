<template>
  <div>
    <b-form class="mb-3">
      <label for="category-input">Select Type & Date:</label>
      <b-form-select
        v-model="selectedDate"
        :options="datesArray"
        @change="objMaker"></b-form-select>
    </b-form>
    <div v-if="fileType && currentDate && fileObj" class="w-100">
      <csvUploadModel :fileObj="fileObj" />
    </div>
  </div>
</template>

<script>
const csvUploadModel = () => import('../airmap/csvUploadmodel.vue');

export default {
  name: 'statusModal',
  props: { statusArray: Array },
  components: { csvUploadModel },
  data() {
    return {
      fields: ['date'],
      selectedDate: null,
      fileType: null,
      currentDate: null,
      fileObj: null,
    };
  },
  computed: {
    datesArray() {
      const datesArray = this.statusArray.map(statusData => {
        return {
          value: `${statusData.type}/${statusData.date}`,
          text: `${statusData.type} --- ${statusData.date}`,
        };
      });
      datesArray.unshift({ value: null, text: 'Please select a date' });
      return datesArray;
    },
  },
  methods: {
    objMaker() {
      const [fileType, currentDate] = this.selectedDate.split('/');
      this.fileType = fileType;
      this.currentDate = currentDate;
      const matchingEntry = this.statusArray.find(
        entry => entry.date === currentDate && entry.type === fileType
      );
      this.fileObj = matchingEntry ? matchingEntry.files : {};
    },
  },
};
</script>

<style scoped></style>
