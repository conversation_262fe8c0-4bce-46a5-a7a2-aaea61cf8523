<template>
  <div>
    <b-form>
      <b-form-group id="input-group-label" label="Name:" label-for="input-label">
        <b-form-input
          id="input-label"
          v-model="form.label"
          :state="validateState('label')"
          type="text"
          required></b-form-input>
        <b-form-invalid-feedback v-if="!form.label.minLength">
          Name must be atleast 3 characters long.
        </b-form-invalid-feedback>
      </b-form-group>
      <b-form-group id="input-group-description" label="Description:" label-for="input-description">
        <b-form-input
          id="input-description"
          v-model="form.description"
          :state="validateState('description')"
          required></b-form-input>
        <b-form-invalid-feedback v-if="!form.description.minLength">
          Description must be atleast 3 characters long.
        </b-form-invalid-feedback>
      </b-form-group>
      <b-form-group id="input-group-startdate" label="Start Date:" label-for="input-startdate">
        <b-form-datepicker
          id="input-startdate"
          v-model="form.startdate"
          :state="validateState('startdate')"
          type="date"
          required
          :min="today"></b-form-datepicker>
        <b-form-invalid-feedback v-if="!form.startdate.required">
          Start Date is required
        </b-form-invalid-feedback>
      </b-form-group>
      <b-form-group id="input-group-enddate" label="End Date:" label-for="input-enddate">
        <b-form-datepicker
          id="input-enddate"
          v-model="form.enddate"
          :state="validateState('enddate')"
          type="date"
          :min="form.startdate"
          required></b-form-datepicker>
        <b-form-invalid-feedback v-if="!form.enddate.required">
          End Date is required
        </b-form-invalid-feedback>
      </b-form-group>
      <b-button class="primaryBg m-2" @click="onSubmit()" :disabled="v$.form.$invalid">
        Submit
      </b-button>
    </b-form>
  </div>
</template>

<script>
import { useVuelidate } from '@vuelidate/core';

import { minLength, required } from '@vuelidate/validators';
export default {
  name: 'createActivityModal',
  setup() {
    return { v$: useVuelidate() };
  },
  data() {
    return {
      form: {
        label: '',
        description: '',
        startdate: '',
        enddate: '',
      },
      today: new Date(),
      priorityOptions: [
        { value: 'High', text: 'High' },
        { value: 'Medium', text: 'Medium' },
        { value: 'Low', text: 'Low' },
      ],
    };
  },
  validations: {
    form: {
      label: {
        required,
        minLength: minLength(3),
      },
      description: {
        required,
        minLength: minLength(3),
      },
      startdate: {
        required,
      },
      enddate: {
        required,
      },
    },
  },
  methods: {
    validateState(name) {
      const { $invalid, $dirty } = this.v$.form[name];
      return $invalid ? $dirty : null;
    },
    onSubmit() {
      this.$store
        .dispatch('homeStore/createNewActivity', this.form)
        .then(response => {
          this.$root.$emit('showToast', {
            message: `${this.form.label} created successfully`,
            title: response.message,
            variant: 'success',
          });
        })
        .catch(error => {
          this.$root.$emit('showToast', {
            title: error.message,
            message: 'There has been an error in creating your activity',
            variant: 'danger',
          });
        });
      this.$bvModal.hide('createActivityModal');
    },
  },
};
</script>

<style></style>
