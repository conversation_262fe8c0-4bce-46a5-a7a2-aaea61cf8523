<template>
  <div class="map-layout">
    <div class="compare-page-btns">
      <b-button @click="$router.go(-1)" title="Back to Map" v-b-tooltip.hover.right>
        <i class="fas fa-arrow-left"></i>
      </b-button>
    </div>
    <b-container>
      <h5 class="text-center font-weight-bold my-3">{{ projectName }} (Analytics)</h5>
      <b-tabs card fill justified active-nav-item-class="font-weight-bold text-primary">
        <b-tab active>
          <template #title>
            <i class="fas fa-object-ungroup mr-2"></i>
            Date Range Analysis
          </template>
          <analyticsSeprate />
        </b-tab>
        <b-tab>
          <template #title>
            <i class="fas fa-chart-simple mr-2"></i>
            Feature Range Analysis
          </template>
          <analyticsGroup />
        </b-tab>
      </b-tabs>
    </b-container>
  </div>
</template>

<script>
const analyticsSeprate = () => import('@/components/modules/airmap/analyticsSeprate.vue');
const analyticsGroup = () => import('@/components/modules/airmap/analyticsGroup.vue');

export default {
  name: 'analyticsPage',
  components: {
    analyticsSeprate,
    analyticsGroup,
  },
  computed: {
    projectName() {
      return this.$store.state.homeStore.projectData.name;
    },
  },
};
</script>

<style scoped>
.map-layout {
  height: 100vh;
}
@media (max-width: 767px) {
  .compare-page-btns {
    padding: 8px;
  }
  .compare-page-btns .btn {
    min-height: 44px;
    padding: 8px 12px;
  }
  .container {
    padding-left: 8px !important;
    padding-right: 8px !important;
  }
  h5 {
    font-size: 1.1rem;
    margin: 16px 0 !important;
  }
  :deep(.nav-tabs .nav-link) {
    padding: 8px 4px;
    font-size: 12px;
    text-align: center;
  }
  :deep(.nav-tabs .nav-link i) {
    display: block;
    margin-bottom: 4px;
    margin-right: 0 !important;
  }
  :deep(.card-body) {
    padding: 12px;
  }
}
@media (max-width: 480px) {
  h5 {
    font-size: 1rem;
  }
  :deep(.nav-tabs .nav-link) {
    padding: 6px 2px;
    font-size: 11px;
  }
}
</style>
