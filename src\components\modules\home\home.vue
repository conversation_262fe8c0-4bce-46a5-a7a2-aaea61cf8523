<template>
  <div>
    <navbar />
    <div class="container-fluid p-3">
      <b-row class="align-items-center">
        <b-col
          cols="12"
          md="6"
          class="d-flex flex-column flex-md-row align-items-md-start align-items-center mb-2 mb-md-0">
          <div class="d-flex flex-wrap mr-md-3 mb-2 mb-md-0">
            <div
              v-for="filterOption in filterOptions"
              :key="filterOption.text"
              @click="filter = filterOption.value"
              class="mr-3">
              <span class="filterOption" :class="{ selectedFilter: filter === filterOption.value }">
                {{ filterOption.text }}
              </span>
            </div>
          </div>
        </b-col>
        <b-col
          cols="12"
          md="6"
          class="d-flex flex-column flex-md-row align-items-center justify-content-md-end">
          <div class="d-flex justify-content-start mb-2 mb-md-0">
            <b-button
              v-if="isAdmin || isManager"
              pill
              class="primaryBg mr-2"
              @click="showCreateModal"
              v-b-tooltip.hover.bottom
              title="Create Project">
              <i class="fa fa-plus"></i>
            </b-button>
            <b-button
              class="primaryBg"
              @click="toggleSortDirection()"
              v-b-tooltip.hover.bottom
              title="Sort Projects">
              <i class="fa fa-sort-amount-down" v-if="sortDirection === 'asc'"></i>
              <i class="fa fa-sort-amount-up" v-else></i>
            </b-button>
          </div>
          <b-input-group class="mb-2 mb-md-0 mx-md-3 w-100 w-md-auto">
            <b-input-group-prepend is-text>
              <i class="fa fa-search"></i>
            </b-input-group-prepend>
            <b-form-input placeholder="Search Project..." v-model="searchBar"></b-form-input>
          </b-input-group>
          <p class="font-weight-bold mb-2 mb-md-0 text-center">
            Showing {{ searchProject.length }} of {{ projectsArray.length }} rows
          </p>
        </b-col>
      </b-row>
      <div v-if="searchProject.length">
        <b-row>
          <b-col
            v-for="(project, index) in paginatedProjects"
            :key="index"
            cols="12"
            md="6"
            lg="3"
            class="p-0">
            <b-card class="card-back" :img-src="project.image" :img-alt="project.name" img-top>
              <b-card-text>
                <div class="projectButtonsContainer d-flex justify-content-end">
                  <b-button
                    v-if="
                      project.status &&
                      project.status[0] &&
                      (project.status[0].time_lapse_video_url ||
                        project.status[0].field_status_video_url)
                    "
                    pill
                    v-b-tooltip.hover
                    title="Current Field Status / Time Lapse View"
                    class="mx-1 projectBtn"
                    @click="showprojectVideoModal(project.status[0])">
                    <i class="fa fa-video"></i>
                  </b-button>
                  <ng-container v-if="isAdmin || isManager">
                    <b-button
                      pill
                      v-b-tooltip.hover
                      title="Project Status"
                      class="mx-1 projectBtn"
                      @click="showStatusModal(project)">
                      <i class="fa fa-briefcase"></i>
                    </b-button>
                    <b-button
                      pill
                      class="mx-1 projectBtn"
                      v-b-tooltip.hover
                      title="Edit Project"
                      @click="showEditProject(project)">
                      <i class="fa fa-pencil"></i>
                    </b-button>
                    <b-button
                      pill
                      class="mx-1 projectBtn"
                      v-b-tooltip.hover
                      title="Share Project"
                      @click="showShareModal(project)">
                      <i class="fa fa-share-alt"></i>
                    </b-button>
                    <b-button
                      pill
                      v-b-tooltip.hover
                      title="Report Download"
                      class="projectBtn"
                      @click="showReportModal(project)">
                      <i class="fa fa-download"></i>
                    </b-button>
                  </ng-container>
                  <b-button
                    pill
                    v-b-tooltip.hover
                    title="Upload Project Files"
                    class="mx-1 projectBtn"
                    @click="routeToFileUpload(project.id)">
                    <i class="fa fa-upload"></i>
                  </b-button>
                </div>
                <div class="d-flex justify-content-between">
                  <ng-container v-if="project.status.length">
                    <b-link class="card-btn card-title text-primary trimmer">
                      <router-link :to="`/map/${project.id}`">
                        {{ nameFormat(project.name) }}
                      </router-link>
                    </b-link>
                  </ng-container>
                  <ng-container v-else class="card-btn card-title m-0 trimmer">
                    {{ nameFormat(project.name) }}
                  </ng-container>
                </div>
                <div class="d-flex flex-column justify-content-between">
                  <p class="text-capitalize my-1 trimmer">
                    <span>{{ project.city }}, {{ project.state }}, {{ project.country }}</span>
                  </p>
                  <p class="m-0 trimmer">
                    Updated On :
                    <span class="font-weight-bolder" v-if="project.status && project.status[0]">
                      {{ project.status[0].date }} ({{ project.status[0].type }})
                    </span>
                    <span class="font-weight-bolder" v-else>---</span>
                  </p>
                </div>
              </b-card-text>
            </b-card>
          </b-col>
        </b-row>
        <div class="my-2 d-flex justify-content-center">
          <b-pagination
            v-if="searchProject.length > itemsPerPage"
            v-model="currentPage"
            :total-rows="searchProject.length"
            :per-page="itemsPerPage"
            first-text="First"
            prev-text="Prev"
            next-text="Next"
            last-text="Last"
            :limit="2"
            pills>
          </b-pagination>
        </div>
      </div>
      <div
        v-else
        class="d-flex flex-column justify-content-center align-items-center text-center mt-4">
        <b-img :src="no_project" alt="Project Not Found" rounded height="350" width="400"></b-img>
        <h2 class="font-weight-bold mt-2">Project Not Found</h2>
      </div>
    </div>
    <b-modal
      id="editProjectModal"
      title="Edit Project"
      size="lg"
      :hide-footer="true"
      centered
      no-close-on-esc
      no-close-on-backdrop>
      <editProject :projectData="projectToBeEdited" />
    </b-modal>
    <b-modal
      id="shareModal"
      :title="shareModalTitle"
      size="lg"
      :hide-footer="true"
      centered
      no-close-on-esc
      no-close-on-backdrop>
      <shareModal :projectData="projectToBeShared" />
    </b-modal>
    <b-modal
      id="statusModal"
      :title="statusModalTitle"
      size="lg"
      :hide-footer="true"
      centered
      no-close-on-esc
      no-close-on-backdrop>
      <statusModal :statusArray="projectStatus" />
    </b-modal>
    <b-modal
      id="reportModal"
      :title="reportModalTitle"
      :hide-footer="true"
      centered
      no-close-on-esc
      no-close-on-backdrop>
      <reportModal :statusArray="projectStatus" />
    </b-modal>
    <b-modal
      size="lg"
      id="createProjectModal"
      title="Create New Project"
      :hide-footer="true"
      centered
      no-close-on-esc
      no-close-on-backdrop>
      <createProject />
    </b-modal>
    <b-modal
      size="lg"
      id="projectVideoModal"
      :title="videoModalTitle"
      :hide-footer="true"
      centered
      no-close-on-esc
      no-close-on-backdrop>
      <videoModal
        :field_status_video_url="field_status_video_url"
        :time_lapse_video_url="time_lapse_video_url" />
    </b-modal>
  </div>
</template>

<script>
import { CATEGORY, formatProjectName } from '@/services/constant';
import keycloakService from '@/services/keycloakService';
import { mapGetters } from 'vuex';

const createProject = () => import('@/components/modules/home/<USER>');
const navbar = () => import('@/components/shared/navbar.vue');
const editProject = () => import('./editProject.vue');
const reportModal = () => import('./reportModal.vue');
const videoModal = () => import('./videoModal.vue');
const shareModal = () => import('./shareModal.vue');
const statusModal = () => import('./statusModal.vue');

export default {
  name: 'home',
  components: {
    navbar,
    editProject,
    shareModal,
    statusModal,
    reportModal,
    videoModal,
    createProject,
  },
  data() {
    return {
      ftpUI: process.env.VUE_APP_UI_FTP_URL,
      no_project: process.env.VUE_APP_NO_PROJECT,
      filterOptions: [
        { text: 'ALL', value: 'all' },
        ...Object.values(CATEGORY).map(format => ({ value: format, text: format.toUpperCase() })),
      ],
      filter: 'all',
      sortDirection: 'desc',
      searchBar: '',
      currentPage: 1,
      itemsPerPage: 8,
      projectToBeEdited: {},
      projectToBeShared: {},
      projectStatus: [],
      statusModalTitle: '',
      reportModalTitle: '',
      shareModalTitle: '',
      videoModalTitle: '',
    };
  },
  computed: {
    ...mapGetters('userStore', ['isAdmin', 'isManager']),
    projectsArray() {
      return this.$store.state.homeStore.projectsArray;
    },
    filteredArray() {
      if (this.filter == 'all') return this.projectsArray;
      return this.projectsArray.filter(project => project.category === this.filter);
    },
    sortedArray() {
      const getDate = project => {
        return project.status && project.status[0] ? new Date(project.status[0].date) : new Date(0);
      };
      const sortFunctions = {
        asc: (a, b) => getDate(a) - getDate(b),
        desc: (a, b) => getDate(b) - getDate(a),
      };
      const sortFn = sortFunctions[this.sortDirection];
      return sortFn ? [...this.filteredArray].sort(sortFn) : this.filteredArray;
    },
    searchProject() {
      if (!this.searchBar) return this.sortedArray;
      const search = this.searchBar.toLowerCase().trim();
      return this.sortedArray.filter(project => project.name.toLowerCase().includes(search));
    },
    paginatedProjects() {
      const start = (this.currentPage - 1) * this.itemsPerPage;
      const end = start + this.itemsPerPage;
      return this.searchProject.slice(start, end);
    },
    nameFormat() {
      return originalName => {
        return formatProjectName(originalName);
      };
    },
  },
  methods: {
    toggleSortDirection() {
      this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';
    },
    routeToFileUpload(project_id) {
      const projectData = {
        project_id,
        portal: 'measure',
      };
      const encodedData = encodeURIComponent(btoa(JSON.stringify(projectData)));
      const token = keycloakService.getCustomToken();
      let url = `${this.ftpUI}?data=${encodedData}`;
      if (token) {
        const tokenData = encodeURIComponent(btoa(JSON.stringify(token)));
        url += `&customToken=${tokenData}`;
      }
      window.open(url, '_blank');
    },
    showprojectVideoModal(project) {
      this.field_status_video_url = project.field_status_video_url;
      this.time_lapse_video_url = project.time_lapse_video_url;
      this.videoModalTitle = `Current Field Status / Time Lapse View for ${project.date}`;
      this.$bvModal.show('projectVideoModal');
    },
    showCreateModal() {
      this.$bvModal.show('createProjectModal');
    },
    showEditProject(project) {
      this.projectToBeEdited = { ...project };
      this.$bvModal.show('editProjectModal');
    },
    showShareModal(project) {
      this.projectToBeShared = { ...project };
      this.shareModalTitle = `Users Access List for ${project.name}`;
      this.$bvModal.show('shareModal');
    },
    showStatusModal(project) {
      this.projectStatus = project.status;
      this.statusModalTitle = `Current Status of ${project.name}`;
      this.$bvModal.show('statusModal');
    },
    showReportModal(project) {
      this.projectStatus = project.status;
      this.reportModalTitle = `Reports Download for ${project.name}`;
      this.$bvModal.show('reportModal');
    },
  },
  watch: {
    searchBar() {
      this.currentPage = 1;
    },
  },
  async mounted() {
    try {
      // Fetch user details
      await this.$store.dispatch('userStore/getUserDetails', false);
      // Fetch all projects
      await this.$store.dispatch('homeStore/getAllProjects', false);
      // If the user is an admin, fetch manage users
      if (this.isAdmin) {
        try {
          await this.$store.dispatch('userStore/getManageUsers', false);
        } catch (error) {
          this.$root.$emit('showToast', {
            title: error.message,
            message: 'There has been a problem in fetching data.',
            variant: 'danger',
          });
        }
      }
    } catch (error) {
      this.$root.$emit('showToast', {
        title: error.message,
        message: 'There has been a problem in fetching data.',
        variant: 'danger',
      });
    }
  },
};
</script>

<style scoped>
.inner_flex .selectedFilter {
  border: none;
  background-color: var(--primary) !important;
  color: var(--transparent-white) !important;
}
.filterOption:hover,
.selectedFilter {
  border-bottom: 4px solid var(--primary);
}
.inner_flex .filterOption:hover {
  border: none;
}
.projectButtonsContainer {
  position: absolute;
  top: 5%;
  right: 5%;
}
.filterOption {
  display: block;
  cursor: pointer;
}
.selectedFilter {
  font-weight: 700;
  display: block;
}
.text-desc {
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}
.card-back {
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
  border: none;
  margin: 0.5rem;
}
@media (max-width: 767px) {
  .filterOption {
    padding: 6px 12px;
    margin: 2px;
    font-size: 14px;
  }
  .card-back {
    margin: 0.25rem;
  }
  .projectButtonsContainer {
    top: 3%;
    right: 3%;
  }
  .projectButtonsContainer .btn {
    padding: 4px 8px;
    min-width: 36px;
    min-height: 36px;
    font-size: 12px;
  }
}
@media (min-width: 768px) and (max-width: 1023px) {
  .container-fluid {
    padding-left: 16px !important;
    padding-right: 16px !important;
  }
  .card-back {
    margin: 0.375rem;
  }
}
@media (min-width: 1024px) {
  .card-back:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.4);
    transition: 0.2s;
  }
}
</style>
