<template>
  <div>
    <b-form v-if="fileObj">
      <b-form-group label="Select Block Wise:" label-for="blockTypeSelect" class="mb-3">
        <b-form-select v-model="block_type" id="blockTypeSelect" class="w-100">
          <b-form-select-option :value="null">Please select an option</b-form-select-option>
          <b-form-select-option v-for="(url, label) in fileObj" :key="label" :value="label">
            {{ label }}
          </b-form-select-option>
        </b-form-select>
      </b-form-group>
      <div v-if="block_type === 'SUMMARY'">
        <b-form-group label="Select File Type:" label-for="summaryFileTypeSelect" class="mb-3">
          <b-form-select v-model="file_type" id="summaryFileTypeSelect" class="w-100">
            <b-form-select-option :value="null">Please select an option</b-form-select-option>
            <b-form-select-option
              v-for="(url, label) in fileObj['SUMMARY']"
              :key="label"
              :value="label">
              {{ label }}
            </b-form-select-option>
          </b-form-select>
        </b-form-group>
      </div>
      <div v-if="block_type === 'INVERTER'">
        <b-form-group label="Select Inverter:" label-for="inverterSelect" class="mb-3">
          <b-form-select v-model="inverter_type" id="inverterSelect" class="w-100">
            <b-form-select-option :value="null">Please select an option</b-form-select-option>
            <b-form-select-option
              v-for="(url, label) in fileObj['INVERTER']"
              :key="label"
              :value="label">
              {{ label }}
            </b-form-select-option>
          </b-form-select>
        </b-form-group>

        <b-form-group
          v-if="inverter_type"
          label="Select File Type:"
          label-for="inverterFileTypeSelect"
          class="mb-3">
          <b-form-select v-model="file_type" id="inverterFileTypeSelect" class="w-100">
            <b-form-select-option :value="null">Please select an option</b-form-select-option>
            <b-form-select-option
              v-for="(url, label) in fileObj['INVERTER'][inverter_type]"
              :key="label"
              :value="label">
              {{ label }}
            </b-form-select-option>
          </b-form-select>
        </b-form-group>
      </div>
      <div class="text-center mt-3" v-if="file_type && block_type">
        <b-button class="primaryBg" @click="downloadFile()">Download</b-button>
      </div>
    </b-form>
  </div>
</template>

<script>
import httpClient from '@/services/httpClient';

export default {
  name: 'csvUploadModel',
  props: ['fileObj'],
  data() {
    return {
      file_type: null,
      block_type: null,
      inverter_type: null,
      file_url: '',
      ftpURL: process.env.VUE_APP_API_FTP_URL,
    };
  },
  methods: {
    async downloadFile() {
      if (this.block_type) {
        if (this.block_type === 'SUMMARY') {
          this.file_url = this.fileObj[this.block_type][this.file_type];
        } else if (this.block_type === 'INVERTER' && this.inverter_type) {
          this.file_url = this.fileObj[this.block_type][this.inverter_type][this.file_type];
        }
      }
      if (this.file_url) {
        const urlParts = this.file_url.split('/');
        const bucketName = urlParts[2]?.split('.')[0];
        const fileKey = urlParts.slice(3).join('/');
        const fileExtension = fileKey.split('.').pop()?.toLowerCase();
        const signedUrl = await this.getSignedUrl(bucketName, fileKey);

        if (signedUrl) {
          this.$root.$emit('showToast', {
            message: 'Please wait..',
            title: 'Downloading Started!!!',
            variant: 'success',
          });
          if (fileExtension === 'pdf') {
            window.open(signedUrl, '_blank');
          } else {
            window.location.assign(signedUrl);
          }
        } else {
          this.$root.$emit('showToast', {
            message: 'Please try again..',
            title: 'Downloading Failed!!!',
            variant: 'danger',
          });
        }
      }
    },
    async getSignedUrl(bucket, key) {
      try {
        const res = await httpClient.get(
          `${this.ftpURL}s3/files/presigned-url?bucket_name=${bucket}&key=${key}`
        );
        return res.data[0].url ?? null;
      } catch (err) {
        return null;
      }
    },
  },
};
</script>

<style></style>
