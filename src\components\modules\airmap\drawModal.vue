<template>
  <div>
    <b-form>
      <b-form-group label="Label*:" label-for="input-label">
        <b-form-input id="input-label" v-model="currentForm.label"></b-form-input>
      </b-form-group>
      <b-form-group label="Description*:" label-for="input-desc">
        <b-form-input id="input-desc" v-model="currentForm.description"></b-form-input>
      </b-form-group>
      <b-form-group
        v-if="showArea"
        :label="aoiShapeType == 'polyline' ? 'Length:' : 'Area:'"
        label-for="input-area">
        <b-form-input id="input-area" :value="formattedArea" disabled></b-form-input>
      </b-form-group>
      <div class="text-center mt-3">
        <b-button
          class="primaryBg mx-2"
          @click="handleSubmit()"
          :disabled="!currentForm.label && !currentForm.description">
          Submit
        </b-button>
        <b-button class="primaryBg" @click="onReset()">Close</b-button>
      </div>
    </b-form>
  </div>
</template>

<script>
export default {
  name: 'DrawModal',
  props: {
    polyDimensionProp: {
      type: [Array, Object],
      default: () => [],
    },
    polygonArea: {
      type: String,
      default: '0',
    },
    aoiShapeType: {
      type: String,
      default: '',
    },
    currentDate: {
      type: String,
      default: () => new Date().toISOString(),
    },
    selectedAoi: {
      type: Object,
      default: () => ({}),
    },
    projectId: {
      type: [Object, String],
      default: null,
    },
  },
  data() {
    return {
      form: this.getInitialForm(),
      dataForm: this.getInitialDataForm(),
    };
  },
  computed: {
    currentForm() {
      return this.isObjectEmpty ? this.form : this.dataForm;
    },
    isObjectEmpty() {
      return Object.keys(this.selectedAoi).length === 0;
    },
    showArea() {
      return ['polygon', 'rectangle', 'circle', 'polyline'].includes(this.aoiShapeType);
    },
    formattedArea() {
      const unit = ['circle', 'polyline'].includes(this.aoiShapeType) ? 'Meters' : 'Hectares';
      return `${this.polygonArea} ${unit}`;
    },
  },
  methods: {
    getInitialForm() {
      return {
        label: '',
        description: '',
        polygon: this.polyDimensionProp,
        area: parseFloat(this.polygonArea),
        aoi_type: this.aoiShapeType,
        id: this.projectId,
        date: this.currentDate,
      };
    },
    getInitialDataForm() {
      return {
        id: this.selectedAoi.id || null,
        label: this.selectedAoi.label || '',
        description: this.selectedAoi.description || '',
        aoi_type: this.selectedAoi.aoi_type || this.aoiShapeType,
        polygon: this.polyDimensionProp,
        area: parseFloat(this.polygonArea),
        is_active: this.selectedAoi.is_active || true,
        is_resolved: false,
      };
    },
    handleSubmit() {
      const actionMethod = this.isObjectEmpty ? this.onSubmit : this.onEdit;
      actionMethod();
    },
    onSubmit() {
      this.$store
        .dispatch('airmapStore/saveAoi', this.form)
        .then(this.handleSuccessResponse(this.form, 'created'))
        .catch(this.handleErrorResponse('creating'));
    },
    onEdit() {
      this.$store
        .dispatch('airmapStore/updateAoi', this.dataForm)
        .then(this.handleSuccessResponse(this.dataForm, 'updated'))
        .catch(this.handleErrorResponse('updating'));
    },
    handleSuccessResponse(formData, action) {
      return response => {
        this.$root.$emit('aoiItemClicked', formData);
        this.$root.$emit('showToast', {
          title: response.message,
          message: `${formData.label} ${action} successfully`,
          variant: 'success',
        });
        this.$bvModal.hide('polygonModal');
      };
    },
    handleErrorResponse(action) {
      return error => {
        this.$root.$emit('showToast', {
          title: error.message,
          message: `There has been a problem in ${action}`,
          variant: 'danger',
        });
      };
    },
    onReset() {
      this.form = this.getInitialForm();
      this.$bvModal.hide('polygonModal');
    },
  },
};
</script>

<style scoped></style>
