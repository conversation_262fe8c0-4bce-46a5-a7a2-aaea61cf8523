import { handleError, handleResponse } from '@/services/constant';
import httpClient from '@/services/httpClient';

const getInitialState = () => ({
  projectsArray: [],
  projectData: {},
  hasProjectFetched: false,
  compareDataLeft: {},
  compareDataRight: {},
  weather: {},
  projectsById: {},
  weatherByProjects: {},
});

const state = getInitialState();

const getters = {
  getSummaryData: state => {
    const { currentDateProject } = state.projectData;
    if (!currentDateProject) return {};

    const { type, summary } = currentDateProject;
    switch (type) {
      case 'SCPM':
        return summary?.summary || {};
      case 'SCQM':
        return summary?.data?.Summary || {};
      case 'CONSTRUCTION':
        return summary || {};
      default:
        return {};
    }
  },
  getInverterData: state => {
    const { currentDateProject } = state.projectData;
    if (!currentDateProject) return { inverters: [], invertersData: [], test: {} };

    const { type, inverter } = currentDateProject;
    let inverterData = {};
    if (type === 'SCPM') {
      inverterData = inverter?.Inverter;
    } else if (type === 'SCQM') {
      inverterData = inverter?.data;
    }
    if (inverterData) {
      const inverters = Object.keys(inverterData);
      const invertersData = Object.values(inverterData);
      return { inverters, invertersData, test: inverterData };
    }
    return { inverters: [], invertersData: [], test: {} };
  },
  getCompareDataTable: state => {
    const summaryLeft = state.compareDataLeft?.summary || {};
    const summaryRight = state.compareDataRight?.summary || {};
    if (Object.keys(summaryLeft).length && Object.keys(summaryRight).length) {
      return Object.keys(summaryRight).map(asset => ({
        Asset: asset,
        'Left Data': summaryLeft[asset] || {},
        'Right Data': summaryRight[asset] || {},
      }));
    }
    return [];
  },
};

const actions = {
  async getAllProjects({ state, commit, dispatch }, dataUpdate = false) {
    if (state.hasProjectFetched && !dataUpdate) {
      return;
    }
    dispatch('toggleSpinner', null, { root: true });
    try {
      const response = await httpClient.get('project/get_all_projects');
      commit('setProjectsArray', response?.data?.data);
      return handleResponse(response);
    } catch (error) {
      return handleError(error);
    } finally {
      dispatch('toggleSpinner', null, { root: true });
    }
  },

  async createProjectApi({ dispatch }, data) {
    dispatch('toggleSpinner', null, { root: true });
    try {
      const response = await httpClient.post('project/create/', data);
      await dispatch('getAllProjects', true);
      return handleResponse(response);
    } catch (error) {
      return handleError(error);
    } finally {
      dispatch('toggleSpinner', null, { root: true });
    }
  },

  async editProjectApi({ dispatch }, data) {
    dispatch('toggleSpinner', null, { root: true });
    try {
      const response = await httpClient.put('project/update/', data);
      await dispatch('getAllProjects', true);
      return handleResponse(response);
    } catch (error) {
      return handleError(error);
    } finally {
      dispatch('toggleSpinner', null, { root: true });
    }
  },

  async getProjectById({ commit, dispatch, state }, payload) {
    const cachedProject = state.projectsById?.[payload.projectId];
    if (cachedProject) {
      commit('setProjectData', cachedProject);
      commit('setCurrentDateProject', cachedProject?.projectdata[0]);
      return Promise.resolve({ status: 'success', data: cachedProject });
    }
    dispatch('toggleSpinner', null, { root: true });
    try {
      const response = await httpClient.get(`project/get_project/${payload.projectId}`);
      const projectData = response?.data?.data;

      commit('cacheProjectById', { id: payload.projectId, data: projectData });
      commit('setProjectData', projectData);
      commit('setCurrentDateProject', projectData?.projectdata[0]);
      return handleResponse(response);
    } catch (error) {
      return handleError(error);
    } finally {
      dispatch('toggleSpinner', null, { root: true });
    }
  },

  async userProjectAccess({ dispatch }, data) {
    dispatch('toggleSpinner', null, { root: true });
    try {
      const response = await httpClient.put('project/add_project_users/', data);
      return handleResponse(response);
    } catch (error) {
      return handleError(error);
    } finally {
      dispatch('toggleSpinner', null, { root: true });
    }
  },

  async getWeatherData({ commit, dispatch, state }, data) {
    const cacheKey = `${data[0]}_${data[1]}`;
    const cachedWeather = state.weatherByProjects[cacheKey];
    if (cachedWeather) {
      commit('setWeatherData', cachedWeather);
      return Promise.resolve({ status: 'success', data: cachedWeather });
    }
    dispatch('toggleSpinner', null, { root: true });
    try {
      const response = await httpClient.get(`project/get_weather/?lat=${data[0]}&lon=${data[1]}`);
      const weatherData = response?.data?.data;

      commit('cacheweatherByProjects', {
        id: cacheKey,
        data: weatherData,
      });
      commit('setWeatherData', weatherData);
      return handleResponse(response);
    } catch (error) {
      return handleError(error);
    } finally {
      dispatch('toggleSpinner', null, { root: true });
    }
  },

  setCompareData({ state, commit }, data) {
    const projectType = state.projectData.currentDateProject?.type;
    if (!projectType) return;
    const matchedData = state.projectData.projectdata.find(
      x => x.date === data.date && x.type === projectType
    );
    if (matchedData) {
      if (data.side === 'left') {
        commit('setCompareLeftData', matchedData);
      } else {
        commit('setCompareRightData', matchedData);
      }
    }
  },

  switchProjectbyDate({ commit, state }, date) {
    const projectType = state.projectData.currentDateProject?.type;
    if (!projectType) return;
    const matchedProject = state.projectData.projectdata.find(
      x => x.date === date && x.type === projectType
    );
    if (matchedProject) {
      commit('setCurrentDateProject', matchedProject);
    }
  },

  switchProjectType({ commit, state }) {
    const { date, type } = state.projectData.currentDateProject || {};
    if (!date || !type) return;
    const matchedProject = state.projectData.projectdata.find(
      x => x.date === date && x.type !== type
    );
    if (matchedProject) {
      commit('setCurrentDateProject', matchedProject);
    }
  },

  resetModule({ commit }) {
    commit('resetModule');
  },
};

const mutations = {
  setProjectsArray(state, data) {
    state.projectsArray = data;
    state.hasProjectFetched = true;
  },
  resetModule(state) {
    Object.assign(state, getInitialState());
  },
  setProjectData(state, data) {
    state.projectData = data;
  },
  setCurrentDateProject(state, data) {
    state.projectData.currentDateProject = data;
  },
  setCompareLeftData(state, data) {
    state.compareDataLeft = data;
  },
  setCompareRightData(state, data) {
    state.compareDataRight = data;
  },
  setWeatherData(state, data) {
    state.weather = data;
  },
  cacheProjectById(state, { id, data }) {
    state.projectsById = { ...state.projectsById, [id]: data };
  },
  cacheweatherByProjects(state, { id, data }) {
    state.weatherByProjects = { ...state.weatherByProjects, [id]: data };
  },
};

export default {
  namespaced: true,
  state,
  getters,
  actions,
  mutations,
};
