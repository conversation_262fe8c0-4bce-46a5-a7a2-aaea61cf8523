<template>
  <div>
    <div class="sidenavListContainer">
      <div>
        <div
          class="d-flex justify-content-between sidenavListItem"
          :class="{
            clickedHighlighter: clickedItemKey === currentDate + activityItem.label,
          }"
          @click="activityItemClicked(activityItem)"
          v-for="(activityItem, key, index) in activityArray"
          :key="index">
          <div>
            <span class="ml-2">{{ activityItem.label }}</span>
          </div>
        </div>
      </div>

      <div class="text-center create-btn">
        <b-button
          pill
          class="primaryBg mt-2 border-0"
          @click="createNewActivity()"
          v-b-tooltip.hover
          title="Create New Activity"
          ><i class="fa fa-plus"></i
        ></b-button>
      </div>
    </div>

    <div v-if="clickedDetailsVisible" class="d-flex flex-column sideBarDetailsContainer">
      <div class="d-flex justify-content-between align-items-center bg-dark rounded-top">
        <b class="ml-2 text-light">{{ clickedDetails.label }}</b>
        <!-- <div role="button" class="mr-2 text-light closeIcon">
            <i
              class="icon fa fa-trash-o"
              aria-controls="sidebar"
              v-b-tooltip.hover
              title="Delete Activity"
              @click="showDeleteModal()"></i>
          </div> -->
        <div role="button" class="mr-2 text-light closeIcon">
          <i
            class="icon fa fa-times"
            aria-controls="sidebar"
            v-b-tooltip.hover
            title="Close Activity"
            @click="closeDetails()"></i>
        </div>
      </div>
      <div class="sideBarDetails d-flex align-items-center rounded-bottom">
        <div class="w-100 p-2">
          <div class="d-flex justify-content-between">
            <span class="w-50"><b>Description</b></span>
            <span class="w-50 text-right">{{ clickedDetails.description }}</span>
          </div>
          <div class="d-flex justify-content-between" v-if="clickedDetails.property">
            <span class="w-50"><b>Count</b></span>
            <span class="w-50 text-right">{{ clickedDetails.property.count }}</span>
          </div>
          <div class="d-flex justify-content-between">
            <span class="w-50"><b>Start Date</b></span>
            <span class="w-50 text-right">{{ clickedDetails.startdate }}</span>
          </div>
          <div class="d-flex justify-content-between">
            <span class="w-50"><b>End Date</b></span>
            <span class="w-50 text-right">{{ clickedDetails.enddate }}</span>
          </div>
        </div>
      </div>
    </div>

    <b-modal
      id="createActivityModal"
      title="Create New Activity"
      :hide-footer="true"
      no-close-on-esc
      no-close-on-backdrop
      centered>
      <createActivityModal />
    </b-modal>
    <b-modal
      id="deleteConfirmModal"
      title="Deletion Confirmation"
      :hide-footer="true"
      no-close-on-esc
      no-close-on-backdrop
      centered>
      <deleteConfirmModal
        :toBeDeleted="toBeDeleted"
        :id="toBeDeletedId"
        :deleteAPI="deleteActivityApi" />
    </b-modal>
  </div>
</template>

<script>
import createActivityModal from '@/components/modules/airmap/sideNav/createActivityModal';
import deleteConfirmModal from '@/components/shared/deleteConfirmModal';

export default {
  name: 'activitySideNav',
  components: { createActivityModal, deleteConfirmModal },
  props: {
    activityArray: Array,
  },
  data() {
    return {
      clickedDetailsVisible: false,
      clickedDetails: {},
      clickedItemKey: '',
      toBeDeleted: '',
      toBeDeletedId: '',
      deleteActivityApi: 'homeStore/deleteActivity',
    };
  },
  computed: {
    currentDate() {
      return this.$store.state.airmapStore.currentDate;
    },
  },
  methods: {
    activityItemClicked(activity) {
      this.$root.$emit('removeKml');
      const project_name = this.$store.state.homeStore.projectName;
      const kml_loc = activity.property.kml_path;
      for (const kml in activity.property.kml) {
        this.$root.$emit('kmlClicked', {
          url: `${kml_loc}${project_name}/activity/${activity.property.kml[kml]}.kml`,
          kml_type: 'activity',
        });
      }
      this.clickedItemKey = this.currentDate + activity.label;
      this.clickedDetails = activity;
      this.clickedDetailsVisible = true;
      this.$root.$emit('activityItemClicked', activity);
    },
    closeDetails() {
      this.$root.$emit('removeKml');
      this.clickedDetails = {};
      this.clickedDetailsVisible = false;
    },
    showDeleteModal(activity) {
      this.toBeDeleted = activity.label;
      this.toBeDeletedId = activity.id;
      this.$bvModal.show('deleteConfirmModal');
    },
    downloadActivity(activity) {
      this.$store.dispatch('airmapStore/downloadActivityApi', activity.id);
    },
    createNewActivity() {
      this.$bvModal.show('createActivityModal');
    },
    removeActivity(activityLabel) {
      if (this.clickedDetails.label === activityLabel) {
        this.clickedDetails = {};
        this.clickedDetailsVisible = false;
        this.clickedItemKey = '';
      }
    },
  },
  mounted() {
    this.$root.$on('activityDeleted', activityLabel => {
      this.removeActivity(activityLabel);
    });
  },
};
</script>

<style scoped></style>
