#!/bin/bash

###################################################################
# Script Name      : build_push_docker_image
# Description      : Builds the Docker image and pushes it to ECR
# Args             : <image-name> <tag> <docker-file> <server-name>
# Author           : Preetam Balijepalli
# Email            :
###################################################################

set -o errexit    # abort script at first error
set -o pipefail   # return the exit status of the last command in the pipe
set -o nounset    # treat unset variables and parameters as an error

image=$1
tag=$2
docker_file=$3
server_name=$4

echo "Image: $image"
echo "Tag: $tag"
echo "Docker file: $docker_file"
echo "Server name: $server_name"

if [ -z "$image" ] || [ -z "$tag" ] || [ -z "$docker_file" ] || [ -z "$server_name" ] ; then
    echo "Usage: $0 <image-name> <tag> <docker-file> <server-name> <ssl-cert-location>"
    exit 1
fi

# Check if aws cli is available
type aws >/dev/null 2>&1 || { echo >&2 "The aws cli is required for this script to run."; exit 1; }

# Get the account number associated with the current IAM credentials
account=$(aws sts get-caller-identity --query Account --output text)

if [ $? -ne 0 ]; then
    exit 255
fi

# Get the region defined in the current configuration (default to us-east-1 if none defined)
region=$(aws configure list | grep region | awk '{print $2}')
region=${region:-ap-south-1}

fullname="${account}.dkr.ecr.${region}.amazonaws.com/${image}:${tag}"

echo "$fullname"

# If the repository doesn't exist in ECR, create it.
aws ecr describe-repositories --repository-names "${image}" || aws ecr create-repository --repository-name "${image}"

# Get the login command from ECR and execute it directly
aws ecr get-login-password --region "${region}" | docker login --username AWS --password-stdin "${account}.dkr.ecr.${region}.amazonaws.com"

# Ensure paths are correctly formatted
# normalized_ssl_cert_location=$(echo "${ssl_cert_location}" | sed 's|\\|/|g')

export MSYS_NO_PATHCONV=1

# Build the Docker image locally with the image name and then push it to ECR with the full name.
docker build -t "${image}" \
    --build-arg SERVER_NAME="${server_name}" \
    -f "${docker_file}" .

# Tag and push the image
docker tag "${image}" "${fullname}"
docker push "${fullname}"

echo "Docker pushed to the repo"

# Check if jq is available
type jq >/dev/null 2>&1 || { echo >&2 "The jq utility is required for this script to run."; exit 1; }

images_to_delete=$(aws ecr list-images --region "${region}" --repository-name "${image}" --filter "tagStatus=UNTAGGED" --query 'imageIds[*]' --output json)

echo "$images_to_delete"

aws ecr batch-delete-image --region "${region}" --repository-name "${image}" --image-ids "$images_to_delete" || true