<template>
  <div id="weather">
    <div class="header">
      <i class="fa-solid fa-temperature-half mr-2"></i>
      Current Weather
    </div>
    <div class="header2 my-1 font-weight-bold">
      <span>{{ weatherData.date }} - {{ weatherData.time }}</span>
      <span class="text-primary">{{ weatherData.city }}, {{ weatherData.country }}</span>
    </div>
    <div class="header3">
      <div class="d-flex justify-content-center align-items-center">
        <span v-b-tooltip.hover.top title="Toggle Unit" class="pointer" @click="toggleUnit()">
          <b-img :src="weather_img" alt="weather" rounded width="80"></b-img>
        </span>
        <p style="font-size: 50px" class="ml-4 m-0">
          {{ unit == 'C' ? weatherData.temperatureC : weatherData.temperatureF }}
          <span class="temp-unit">°{{ unit }}</span>
        </p>
      </div>
    </div>
    <div class="header4 my-1 text-center">
      <div v-for="[key, value] in Object.entries(myObject)" :key="key" class="header4-part">
        <p class="value1 m-0">{{ value }}</p>
        <p class="key1 m-0">{{ key }}</p>
      </div>
    </div>
  </div>
</template>

<script>
import { Country } from 'country-state-city';
export default {
  name: 'weather',
  props: {
    center: {
      type: Array,
      required: true,
    },
  },
  computed: {
    getCenter() {
      const center = this.$store.state.homeStore.projectData.center;
      if (center) {
        const [lat, lng] = center.split(',').map(coord => parseFloat(coord.trim()));
        if (!isNaN(lat) && !isNaN(lng)) return [lat, lng];
      }
      return [];
    },
  },
  data() {
    return {
      unit: 'C',
      weatherData: {},
      myObject: {},
      weather_img: process.env.VUE_APP_WEATHER,
    };
  },
  methods: {
    fetchWeatherData(data) {
      const countryCode = data.sys?.country;
      this.weatherData = {
        country: Country.getCountryByCode(countryCode).name,
        city: data.name,
        temperatureC: Math.ceil(data.main?.temp),
        temperatureF: Math.ceil((Math.ceil(data.main?.temp) * 9) / 5 + 32),
        date: this.formatDate(new Date(data.dt * 1000)),
        time: this.formatTime(new Date(data.dt * 1000)),
      };
      this.myObject = {
        'prec in 24hrs': data.rain?.['1h'] || 0.0 + ' mm',
        humidity: data.main?.humidity + ' %',
        'wind speed': data.wind?.speed + ' m/s',
        'feels like': Math.ceil(data.main?.feels_like) + ' °C',
      };
    },
    toggleUnit() {
      this.unit = this.unit === 'C' ? 'F' : 'C';
    },
    formatDate(date) {
      const day = date.getDate();
      const monthNames = [
        'January',
        'February',
        'March',
        'April',
        'May',
        'June',
        'July',
        'August',
        'September',
        'October',
        'November',
        'December',
      ];
      const month = monthNames[date.getMonth()];
      const year = date.getFullYear();
      return `${day} ${month}, ${year}`;
    },
    formatTime(date) {
      const hours = date.getHours().toString().padStart(2, '0');
      const minutes = date.getMinutes().toString().padStart(2, '0');
      const seconds = date.getSeconds().toString().padStart(2, '0');
      // const timeZone = Intl.DateTimeFormat().resolvedOptions().timeZone;
      // return `${hours}:${minutes}:${seconds} ${timeZone}`;
      return `${hours}:${minutes}:${seconds}`;
    },
  },
  mounted() {
    this.$store
      .dispatch('homeStore/getWeatherData', this.getCenter)
      .then(data => {
        this.fetchWeatherData(data.data);
      })
      .catch(error => {
        this.$root.$emit('showToast', {
          title: error.message,
          message: 'There has been a problem in fetching data',
          variant: 'danger',
        });
      });
  },
};
</script>

<style scoped>
#weather {
  min-width: 300px;
  border: solid 2px var(--primary);
  transition: display 0.3s ease-in-out;
  padding: 5px;
  background-color: var(--transparent-white);
  border-radius: 10px;
}
.header {
  text-align: left;
  font-size: 16px;
  padding: 5px;
  font-weight: 600;
  background-color: var(--primary);
  color: var(--transparent-white);
  border-radius: 5px;
}
.header2 {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.temp-unit {
  font-size: 20px;
  position: absolute;
  padding-top: 10px;
  vertical-align: top;
}
.header4 {
  display: flex;
  justify-content: space-evenly;
  align-items: center;
}
.header4 .header4-part {
  display: flex;
  flex-direction: column;
  align-items: center;
  border-right: solid 1px var(--grey);
  padding: 4px;
}
.header4 .header4-part .value1 {
  font-size: 13px;
  font-weight: bold;
}
</style>
