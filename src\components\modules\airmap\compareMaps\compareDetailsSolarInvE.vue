<template>
  <div class="compareDetailsContainer" v-if="labels && LeftTransformed && RightTransformed">
    <div class="d-flex justify-content-between mb-2">
      <div v-if="projectType == 'SCPM'" class="d-flex w-50">
        <b-form-select
          v-model="selectedInv"
          :options="parentCategory"
          @change="getChildCategory()"
          value="selectedInv"></b-form-select>
        <b-form-select
          class="ml-3"
          v-model="selectedAsset"
          :options="childCategory"
          @change="getData()"
          value="selectedAsset"></b-form-select>
      </div>
      <div v-if="projectType == 'SCQM'">
        <b-form-select
          v-model="selectedInv"
          :options="parentCategory"
          @change="getData()"
          value="selectedAsset"></b-form-select>
      </div>
      <VueToggles
        @click="isChart = !isChart"
        :value="isChart"
        height="25"
        width="80"
        checkedText="Chart"
        uncheckedText="Table"
        checkedBg="darkcyan"
        uncheckedBg="darkcyan"
        fontSize="20px"
        fontWeight="500" />
    </div>
    <div v-if="isChart">
      <div class="chartContainer mx-auto">
        <apexchart type="bar" :options="options" :series="series"></apexchart>
      </div>
    </div>
    <div v-else>
      <b-table
        class="table-style"
        bordered
        striped
        responsive
        hover
        head-variant="dark"
        :items="paginatedData"
        :fields="fields"></b-table>
      <b-pagination
        class="justify-content-center m-0"
        v-model="currentPage"
        :total-rows="tableData.length"
        :per-page="perPage"
        first-text="First"
        prev-text="Prev"
        next-text="Next"
        last-text="Last"
        pills></b-pagination>
    </div>
  </div>
</template>

<script>
import { SOLAR_TYPE } from '@/services/constant';
import { mapState } from 'vuex';
export default {
  name: 'compareDetailsSolarInvE',
  data() {
    return {
      isChart: false,
      perPage: 5, // Number of items per page
      currentPage: 1, // Current page
      selectedAsset: null,
      selectedInv: null,
      labelsObjLeft: [],
      labelsObjRight: [],
      LeftTransformed: [],
      RightTransformed: [],
    };
  },
  computed: {
    ...mapState({
      projectType: state => state.homeStore.projectData.currentDateProject.type,
      leftDate: state => state.homeStore.compareDataLeft.date,
      rightDate: state => state.homeStore.compareDataRight.date,
      summaryLeft: state => state.homeStore.compareDataLeft?.inverter?.Inverter ?? {},
      summaryRight: state => state.homeStore.compareDataRight?.inverter?.Inverter ?? {},
      deviationLeft: state => state.homeStore.compareDataLeft?.inverter_deviation?.data ?? {},
      deviationRight: state => state.homeStore.compareDataRight?.inverter_deviation?.data ?? {},
    }),
    options() {
      return {
        chart: {
          id: 'vuechart-compare',
        },
        xaxis: {
          categories: this.labels,
          labels: {
            style: {
              fontSize: '12px',
              fontFamily: 'Helvetica, Arial, sans-serif',
              fontWeight: 600,
              cssClass: 'apexcharts-xaxis-label',
            },
          },
        },
        yaxis: {
          labels: {
            style: {
              fontSize: '12px',
              fontFamily: 'Helvetica, Arial, sans-serif',
              fontWeight: 600,
              cssClass: 'apexcharts-yaxis-label',
            },
          },
        },
        plotOptions: {
          bar: {
            horizontal: false,
            dataLabels: {
              position: 'bottom',
            },
          },
        },
        legend: {
          fontSize: '20px',
          fontFamily: 'Helvetica, Arial',
          fontWeight: 400,
          itemMargin: {
            horizontal: 10,
            vertical: 5,
          },
        },
        dataLabels: {
          enabled: true,
          enabledOnSeries: undefined,
          textAnchor: 'middle',
          distributed: false,
          offsetX: 0,
          offsetY: 0,
          style: {
            fontSize: '14px',
            fontFamily: 'Helvetica, Arial, sans-serif',
            fontWeight: 'bold',
            colors: ['#fff'],
          },
          background: {
            enabled: true,
            foreColor: '#000',
            padding: 4,
            borderRadius: 2,
            borderWidth: 1,
            borderColor: '#fff',
            opacity: 0.9,
            dropShadow: {
              enabled: false,
              top: 1,
              left: 1,
              blur: 1,
              color: '#000',
              opacity: 0.45,
            },
          },
          dropShadow: {
            enabled: false,
            top: 1,
            left: 1,
            blur: 1,
            color: '#000',
            opacity: 0.45,
          },
        },
      };
    },
    series() {
      return [
        {
          name: this.leftDate,
          data: Array.isArray(this.LeftTransformed) ? this.LeftTransformed.map(x => x.value) : [],
        },
        {
          name: this.rightDate,
          data: Array.isArray(this.RightTransformed) ? this.RightTransformed.map(x => x.value) : [],
        },
      ];
    },
    fields() {
      const fieldData = [
        { key: 'Asset', label: this.selectedAsset, sortable: true },
        { key: 'Left Data', label: this.leftDate, sortable: true },
        { key: 'Right Data', label: this.rightDate, sortable: true },
        { key: 'Progress', label: 'Progress (%)', sortable: true },
      ];
      return fieldData;
    },
    labels() {
      if (this.RightTransformed && this.LeftTransformed) {
        const leftKeys = Array.isArray(this.LeftTransformed)
          ? this.LeftTransformed.map(x => x.key)
          : [];
        const rightKeys = Array.isArray(this.RightTransformed)
          ? this.RightTransformed.map(x => x.key)
          : [];
        return [...new Set([...leftKeys, ...rightKeys])];
      } else {
        return [];
      }
    },
    paginatedData() {
      const start = (this.currentPage - 1) * this.perPage;
      const end = start + this.perPage;
      return this.tableData().slice(start, end);
    },
  },
  methods: {
    getLables() {
      if (this.projectType == SOLAR_TYPE.SCPM) {
        this.labelsObjLeft = this.summaryLeft;
        this.labelsObjRight = this.summaryRight;
      } else if (this.projectType == SOLAR_TYPE.SCQM) {
        this.labelsObjLeft = this.deviationLeft;
        this.labelsObjRight = this.deviationRight;
      }
      this.getParentCategory();
    },
    getParentCategory() {
      if (this.labelsObjLeft && this.labelsObjRight) {
        this.parentCategory = [
          ...new Set([...Object.keys(this.labelsObjLeft), ...Object.keys(this.labelsObjRight)]),
        ];
        this.selectedInv = this.parentCategory[0];
      }
      this.getChildCategory();
    },
    getChildCategory() {
      if (this.parentCategory) {
        let child = '';
        try {
          child = this.labelsObjLeft[this.selectedInv];
        } catch {
          child = this.labelsObjRight[this.selectedInv];
        }
        if (child) {
          this.childCategory = Object.keys(child);
          this.selectedAsset = this.childCategory[0];
          this.getData();
        }
      }
    },
    getData() {
      this.summaryLeftData();
      this.summaryRightData();
    },
    async summaryLeftData() {
      if (this.projectType == SOLAR_TYPE.SCPM) {
        this.LeftTransformed = await this.objectToArray(
          this.summaryLeft[this.selectedInv][this.selectedAsset]
        );
      } else if (this.projectType == SOLAR_TYPE.SCQM) {
        this.LeftTransformed = await this.objectToArray(this.deviationLeft[this.selectedInv]);
      }
    },
    async summaryRightData() {
      if (this.projectType == SOLAR_TYPE.SCPM) {
        this.RightTransformed = await this.objectToArray(
          this.summaryRight[this.selectedInv][this.selectedAsset]
        );
      } else if (this.projectType == SOLAR_TYPE.SCQM) {
        this.RightTransformed = await this.objectToArray(this.deviationRight[this.selectedInv]);
      }
    },
    async objectToArray(data) {
      const requiredArray = [];
      for (const key in data) {
        try {
          requiredArray.push({ key: key, value: data[key]?.actual ?? data[key]?.Actual ?? 0 });
        } catch {
          requiredArray.push(0);
        }
      }
      return await requiredArray;
    },
    tableData() {
      const tableData = [];
      if (this.labels && this.LeftTransformed && this.RightTransformed) {
        for (let i = 0; i < this.labels.length; i++) {
          const leftData = parseInt(this.LeftTransformed[i].value || 0);
          const rightData = parseInt(this.RightTransformed[i].value || 0);
          const progress =
            leftData > rightData
              ? ((leftData - rightData) / leftData) * 100
              : ((rightData - leftData) / rightData) * 100;
          const progressValue = isNaN(progress) ? 0.0 : progress;
          tableData.push({
            Asset: this.labels[i],
            'Left Data': leftData,
            'Right Data': rightData,
            Progress: progressValue.toFixed(2),
          });
        }
      }
      return tableData;
    },
  },
  mounted() {
    this.getLables();
  },
};
</script>

<style scoped>
.compareDetailsContainer {
  display: flex;
  flex-direction: column;
  width: 100%;
}
</style>
