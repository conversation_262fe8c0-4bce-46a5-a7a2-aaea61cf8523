<template>
  <div>
    <sidenav />
    <airmap />
  </div>
</template>

<script>
const airmap = () => import('@/components/modules/airmap/airmap.vue');
const sidenav = () => import('@/components/modules/airmap/sideNav/sidenav.vue');
import router from '@/router';
import store from '@/store';

export default {
  name: 'mapPage',
  components: {
    airmap,
    sidenav,
  },
  beforeRouteEnter(to, from, next) {
    const projectId = to.params.projectId;
    if (projectId) {
      store
        .dispatch('homeStore/getProjectById', {
          projectId,
          page: 'map',
        })
        .then(() => next())
        .catch(() => router.push('/home'));
    } else {
      router.push('/home');
    }
  },
};
</script>
