<template>
  <div>
    <div class="logo-container" v-if="isSpinnerShowing">
      <b-spinner label="Loading..." class="spinner"></b-spinner>
      <b-img :src="imageURL" rounded thumbnail fluid></b-img>
    </div>
    <router-view />
  </div>
</template>
<script>
export default {
  computed: {
    isSpinnerShowing() {
      return this.$store.state.isSpinnerShowing;
    },
    imageURL() {
      return this.$store.state.userStore.loggedInUser?.sidebar_logo;
    },
  },
  methods: {
    handleToast({ message, title = 'Info', time = '3000', variant = 'success' }) {
      this.$bvToast.toast(message, {
        title: title,
        autoHideDelay: time,
        appendToast: true,
        variant: variant,
      });
    },
  },
  created() {
    document.documentElement.style.setProperty('--primary', process.env.VUE_APP_PRIMARY_COLOR);
  },
  mounted() {
    this.$root.$on('showToast', toastData => {
      if (toastData) this.handleToast(toastData);
    });
  },
};
</script>
<style></style>
