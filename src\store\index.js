import { SESSION_TIMEOUT } from '@/services/constant';
import { decryptData, encryptData, generateKey } from '@/services/dataService';
import localforage from 'localforage';
import { createStore } from 'vuex';
import VuexPersistence from 'vuex-persist';
import airmapStore from './modules/airmapStore.js';
import homeStore from './modules/homeStore.js';
import userStore from './modules/userStore.js';

const localforageInstance = localforage.createInstance({
  name: 'measure-datasee-ai',
  storeName: 'vuex',
  driver: localforage.INDEXEDDB,
});

const vuexLocal = new VuexPersistence({
  asyncStorage: true,
  storage: {
    async getItem(key) {
      await generateKey();
      try {
        const encryptedData = await localforageInstance.getItem(key);
        if (!encryptedData) return null;
        const decryptedData = decryptData(encryptedData);
        // Session expiration check
        const sessionAge = Date.now() - decryptedData.loginTime;
        if (sessionAge > SESSION_TIMEOUT) {
          localStorage.clear();
          await localforageInstance.clear();
          console.clear();
          return null;
        }
        return decryptedData;
      } catch (error) {
        console.error(`Failed to retrieve item from IndexedDB: ${error}`);
        return null;
      }
    },
    async setItem(key, value) {
      await generateKey();
      try {
        const valueWithTimestamp = {
          ...value,
          loginTime: value.loginTime || Date.now(),
        };
        const encryptedData = encryptData(valueWithTimestamp);
        await localforageInstance.setItem(key, encryptedData);
      } catch (error) {
        console.error(`Failed to set item in IndexedDB: ${error}`);
      }
    },
    async removeItem(key) {
      try {
        await localforageInstance.removeItem(key);
      } catch (error) {
        console.error(`Failed to remove item from IndexedDB: ${error}`);
      }
    },
  },
});

// Initial state
const getInitialState = () => ({
  isSpinnerShowing: false,
  loginTime: Date.now(),
});

// Create the Vuex store
const store = createStore({
  state: getInitialState(),
  actions: {
    toggleSpinner({ commit }) {
      commit('toggleSpinner');
    },
    async logout({ commit, dispatch }) {
      dispatch('homeStore/resetModule', null, { root: true });
      dispatch('userStore/resetModule', null, { root: true });
      dispatch('airmapStore/resetModule', null, { root: true });
      await commit('resetRoot');
    },
  },
  mutations: {
    toggleSpinner(state) {
      state.isSpinnerShowing = !state.isSpinnerShowing;
    },
    async resetRoot(state) {
      try {
        Object.assign(state, getInitialState());
        localStorage.clear();
        await localforageInstance.clear();
        console.clear();
      } catch (error) {
        console.error(`Failed to reset IndexedDB: ${error}`);
      }
    },
  },
  modules: {
    homeStore,
    userStore,
    airmapStore,
  },
  plugins: [vuexLocal.plugin],
});

export default store;
