import { handleError, handleResponse } from '@/services/constant';
import httpClient from '@/services/httpClient';

const getInitialState = () => ({
  loggedInUser: {},
  manageUsers: [],
  roles: [],
});

const state = getInitialState();

const getters = {
  isAdmin: state => state.loggedInUser && state.loggedInUser.role === 'admin',
  isManager: state => state.loggedInUser && state.loggedInUser.role === 'manager',
  isPilot: state => state.loggedInUser && state.loggedInUser.role === 'pilot',
};

const actions = {
  async register({ dispatch }, userData) {
    dispatch('toggleSpinner', null, { root: true });
    try {
      const response = await httpClient.post('accounts/user_register/', userData);
      return handleResponse(response);
    } catch (error) {
      return handleError(error);
    } finally {
      dispatch('toggleSpinner', null, { root: true });
    }
  },
  async updateUserDetails({ dispatch }, userData) {
    dispatch('toggleSpinner', null, { root: true });
    try {
      const response = await httpClient.put('accounts/user_update/', userData);
      await dispatch('getUserDetails', true);
      return handleResponse(response);
    } catch (error) {
      return handleError(error);
    } finally {
      dispatch('toggleSpinner', null, { root: true });
    }
  },
  async generateLink({ dispatch }, data) {
    dispatch('toggleSpinner', null, { root: true });
    try {
      const response = await httpClient.post('accounts/generate_link/', data);
      return handleResponse(response);
    } catch (error) {
      return handleError(error);
    } finally {
      dispatch('toggleSpinner', null, { root: true });
    }
  },
  async resetPassword({ dispatch }, userCredentials) {
    dispatch('toggleSpinner', null, { root: true });
    try {
      const response = await httpClient.put('accounts/reset_password/', userCredentials);
      return handleResponse(response);
    } catch (error) {
      return handleError(error);
    } finally {
      dispatch('toggleSpinner', null, { root: true });
    }
  },
  async getUserDetails({ state, commit, dispatch }, dataUpdate) {
    if (!dataUpdate && Object.keys(state.loggedInUser).length > 0) return;

    dispatch('toggleSpinner', null, { root: true });
    try {
      const response = await httpClient.get('accounts/user_details/');
      commit('setLoggedInUser', response?.data?.data);
      return handleResponse(response);
    } catch (error) {
      return handleError(error);
    } finally {
      dispatch('toggleSpinner', null, { root: true });
    }
  },
  async getManageUsers({ state, commit, dispatch }, dataUpdate) {
    if (!dataUpdate && state.manageUsers.length > 0) return;
    dispatch('toggleSpinner', null, { root: true });
    try {
      const groupId = state.loggedInUser?.company?.id;
      if (!groupId) return Promise.resolve([]);

      const response = await httpClient.get(`accounts/manage_users/${groupId}`);
      dispatch('getAllRoles');
      commit('setManageUsers', response?.data?.data);
      return handleResponse(response);
    } catch (error) {
      return handleError(error);
    } finally {
      dispatch('toggleSpinner', null, { root: true });
    }
  },
  async getAllRoles({ state, commit, dispatch }) {
    if (state.roles.length > 0) return;
    dispatch('toggleSpinner', null, { root: true });
    try {
      const response = await httpClient.get('accounts/role/');
      commit('setRoles', response?.data?.data);
      return handleResponse(response);
    } catch (error) {
      return handleError(error);
    } finally {
      dispatch('toggleSpinner', null, { root: true });
    }
  },
  async updateRoleAccess({ dispatch }, data) {
    dispatch('toggleSpinner', null, { root: true });
    try {
      const response = await httpClient.put('accounts/role_access/', data);
      return handleResponse(response);
    } catch (error) {
      return handleError(error);
    } finally {
      dispatch('toggleSpinner', null, { root: true });
    }
  },
  async updateUserActiveAccess({ dispatch }, data) {
    dispatch('toggleSpinner', null, { root: true });
    try {
      const response = await httpClient.put('accounts/active_access/', data);
      return handleResponse(response);
    } catch (error) {
      return handleError(error);
    } finally {
      dispatch('toggleSpinner', null, { root: true });
    }
  },
  async isEmailUnique({ dispatch }, data) {
    dispatch('toggleSpinner', null, { root: true });
    try {
      const response = await httpClient.post('accounts/is_email_unique/', data);
      return handleResponse(response);
    } catch (error) {
      return handleError(error);
    } finally {
      dispatch('toggleSpinner', null, { root: true });
    }
  },
  resetModule({ commit }) {
    commit('resetModule');
  },
};

const mutations = {
  setManageUsers(state, manageUsers) {
    state.manageUsers = manageUsers;
  },
  setRoles(state, roles) {
    state.roles = roles;
  },
  setLoggedInUser(state, userData) {
    state.loggedInUser = userData;
  },
  resetModule(state) {
    Object.assign(state, getInitialState());
  },
};

export default {
  namespaced: true,
  state,
  getters,
  actions,
  mutations,
};
