user nginx;
worker_processes auto;
error_log /var/log/nginx/error.log warn;
pid /var/run/nginx.pid;

events {
    worker_connections 1024;
}

http {
    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    proxy_headers_hash_max_size 1024;
    proxy_headers_hash_bucket_size 128;

    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for"';

    access_log /var/log/nginx/access.log main;
    sendfile on;
    keepalive_timeout 65;

    #####################################
    #         GZIP Settings              #
    #####################################

    gzip on;
    gzip_http_version 1.1;
    gzip_disable "MSIE [1-6]\.";
    gzip_min_length 256;
    gzip_vary on;
    gzip_proxied expired no-cache no-store private auth;
    gzip_types text/plain text/css application/json application/javascript application/x-javascript text/xml application/xml application/xml+rss;
    gzip_comp_level 9;
    gzip_buffers 32 8k;

    server {
        listen 443 ssl;
        listen [::]:443 ssl;

        root /usr/share/nginx/html;  # Ensure this path is correct
        index index.html;

        server_name __SERVER_NAME__;

        #####################################
        #         SSL Settings              #
        #####################################

        ssl_certificate /etc/letsencrypt/live/__SERVER_NAME__/fullchain.pem;
        ssl_certificate_key /etc/letsencrypt/live/__SERVER_NAME__/privkey.pem;

        ssl_session_timeout 1d;
        ssl_session_cache shared:SSL:50m;
        ssl_session_tickets off;

        ssl_protocols TLSv1 TLSv1.1 TLSv1.2;
        ssl_prefer_server_ciphers on;
        ssl_ciphers 'EECDH+AESGCM:EDH+AESGCM:AES256+EECDH:AES256+EDH';

        server_tokens off;

        # Static files handling
        location ~* \.(css|js)$ {
            root /usr/share/nginx/html;  # Ensure this path is correct
            add_header Cache-Control 'max-age=31536000'; # Cache for one year
            try_files $uri =404;  # Return 404 if file not found
        }

        # Handle JSON and HTML files
        location ~* \.(json|html)$ {
            root /usr/share/nginx/html;  # Ensure this path is correct
            expires -1;
            add_header Cache-Control 'no-store, no-cache, must-revalidate, proxy-revalidate, max-age=0';
        }

        location / {
            try_files $uri $uri/ /index.html;

            add_header Set-Cookie "name=value; SameSite=Strict;";
            add_header Cache-Control "public, max-age=31536000";
        }

        error_page 500 502 503 504 /50x.html;
        location = /50x.html {
            root /usr/share/nginx/html;
        }
    }

    server {
        listen 80;
        listen [::]:80;
        server_name __SERVER_NAME__;

        # Redirect to HTTPS
        location / {
            return 301 https://__SERVER_NAME__$request_uri;
        }
    }
}
