<template>
  <b-form @submit.prevent="onSubmit">
    <b-row>
      <b-col cols="12" md="8">
        <b-form-group label="Project Name:" label-for="name-input">
          <b-form-input
            class="w-100"
            id="name-input"
            v-model="form.name"
            placeholder="Enter project name"
            @blur="v$.form.name.$touch()" />
          <b-form-invalid-feedback :state="validateState('name')">
            Name must be at least 5 characters long.
          </b-form-invalid-feedback>
        </b-form-group>
      </b-col>
      <b-col cols="12" md="4">
        <b-form-group label="Category:" label-for="category-input">
          <b-form-input class="w-100" id="category-input" v-model="form.category" disabled />
        </b-form-group>
      </b-col>
    </b-row>
    <b-row>
      <b-col cols="12">
        <b-form-group label="Description:" label-for="description-input">
          <b-form-textarea
            class="w-100"
            id="description-input"
            v-model="form.description"
            placeholder="Enter project description"
            @blur="v$.form.description.$touch()" />
          <b-form-invalid-feedback :state="validateState('description')">
            Description must be at least 5 characters long.
          </b-form-invalid-feedback>
        </b-form-group>
      </b-col>
    </b-row>
    <b-row>
      <b-col cols="12" md="4">
        <b-form-group label="Country:" label-for="country-input">
          <b-form-input class="w-100" id="country-input" v-model="form.country" disabled />
        </b-form-group>
      </b-col>
      <b-col cols="12" md="4">
        <b-form-group label="State:" label-for="state-input">
          <b-form-input class="w-100" id="state-input" v-model="form.state" disabled />
        </b-form-group>
      </b-col>
      <b-col cols="12" md="4">
        <b-form-group label="City:" label-for="city-input">
          <b-form-input class="w-100" id="city-input" v-model="form.city" disabled />
        </b-form-group>
      </b-col>
    </b-row>
    <div class="text-center">
      <b-button class="primaryBg" @click="onSubmit" :disabled="v$.form.$invalid"> Submit </b-button>
    </div>
  </b-form>
</template>

<script>
import { useVuelidate } from '@vuelidate/core';
import { minLength, required } from '@vuelidate/validators';

export default {
  name: 'editProject',
  props: ['projectData'],
  setup() {
    return { v$: useVuelidate() };
  },
  data() {
    return {
      form: this.projectData,
    };
  },
  validations() {
    return {
      form: {
        name: {
          required,
          minLength: minLength(5),
        },
        category: {
          required,
        },
        description: {
          required,
          minLength: minLength(5),
        },
        country: {
          required,
        },
        state: {
          required,
        },
        city: {
          required,
        },
      },
    };
  },
  methods: {
    validateState(name) {
      const field = this.v$.form[name];
      return field.$invalid && field.$dirty ? false : null;
    },
    onSubmit() {
      const projectUpdateData = {
        id: this.form.id,
        name: this.form.name,
        state: this.form.state,
        country: this.form.country,
        city: this.form.city,
        description: this.form.description,
      };
      this.$store
        .dispatch('homeStore/editProjectApi', projectUpdateData)
        .then(response => {
          this.$root.$emit('showToast', {
            name: response.message,
            message: 'Your changes are saved successfully',
            variant: 'success',
          });
          this.$bvModal.hide('editProjectModal');
        })
        .catch(error => {
          this.$root.$emit('showToast', {
            title: error.message,
            message: 'There has been a problem in fetching data.',
            variant: 'danger',
          });
        });
    },
  },
};
</script>

<style scoped>
@media (max-width: 767px) {
  .form-group {
    margin-bottom: 16px;
  }
  .form-control {
    min-height: 44px;
  }
  textarea.form-control {
    min-height: 80px;
  }
}
</style>
