<template>
  <div class="compareDetailsContainer" v-if="labels && LeftTransfromed && RightTransformed">
    <div class="d-flex justify-content-between mb-2">
      <div v-if="projectType == 'SCPM'" class="d-flex w-50">
        <b-form-select
          v-model="selectedAsset"
          :options="parentCategory"
          @change="getChildCategory()"
          value="selectedAsset"></b-form-select>
        <b-form-select
          class="ml-3"
          v-model="selectedFea"
          :options="childCategory"
          @change="getData()"
          value="selectedFea"></b-form-select>
      </div>
      <div v-if="projectType == 'SCQM'">
        <b-form-select
          v-model="selectedAsset"
          :options="parentCategory"
          @change="getData()"
          value="selectedAsset"></b-form-select>
      </div>
      <VueToggles
        @click="isChart = !isChart"
        :value="isChart"
        height="25"
        width="80"
        checkedText="Chart"
        uncheckedText="Table"
        checkedBg="darkcyan"
        uncheckedBg="darkcyan"
        fontSize="20px"
        fontWeight="500" />
    </div>
    <div v-if="isChart">
      <div class="chartContainer mx-auto">
        <apexchart type="bar" :options="options" :series="series"></apexchart>
      </div>
    </div>
    <div v-else>
      <b-table
        class="table-style"
        bordered
        striped
        responsive
        hover
        head-variant="dark"
        :items="paginatedData"
        :fields="fields"></b-table>
      <b-pagination
        class="justify-content-center m-0"
        v-model="currentPage"
        :total-rows="tableData.length"
        :per-page="perPage"
        first-text="First"
        prev-text="Prev"
        next-text="Next"
        last-text="Last"
        pills></b-pagination>
    </div>
  </div>
</template>

<script>
import { SOLAR_TYPE } from '@/services/constant';
import { mapState } from 'vuex';

export default {
  name: 'compareDetailsSolarInv',
  data() {
    return {
      isChart: false,
      perPage: 5, // Number of items per page
      currentPage: 1, // Current page
      selectedAsset: null,
      selectedFea: null,
      labels: [],
      labelsObjLeft: [],
      labelsObjRight: [],
      LeftTransfromed: [],
      RightTransformed: [],
    };
  },
  computed: {
    ...mapState({
      projectType: state => state.homeStore.projectData.currentDateProject.type,
      leftDate: state => state.homeStore.compareDataLeft.date,
      rightDate: state => state.homeStore.compareDataRight.date,
      summaryLeft: state => state.homeStore.compareDataLeft?.inverter?.Inverter ?? {},
      summaryRight: state => state.homeStore.compareDataRight?.inverter?.Inverter ?? {},
      deviationLeft: state => state.homeStore.compareDataLeft?.inverter_deviation?.data ?? {},
      deviationRight: state => state.homeStore.compareDataRight?.inverter_deviation?.data ?? {},
    }),
    options() {
      return {
        chart: {
          id: 'vuechart-compare',
        },
        xaxis: {
          categories: this.labels,
          labels: {
            style: {
              fontSize: '12px',
              fontFamily: 'Helvetica, Arial, sans-serif',
              fontWeight: 600,
              cssClass: 'apexcharts-xaxis-label',
            },
          },
        },
        yaxis: {
          labels: {
            style: {
              fontSize: '12px',
              fontFamily: 'Helvetica, Arial, sans-serif',
              fontWeight: 600,
              cssClass: 'apexcharts-yaxis-label',
            },
          },
        },
        plotOptions: {
          bar: {
            horizontal: false,
            dataLabels: {
              position: 'bottom',
            },
          },
        },
        legend: {
          fontSize: '20px',
          fontFamily: 'Helvetica, Arial',
          fontWeight: 400,
          itemMargin: {
            horizontal: 10,
            vertical: 5,
          },
        },
        dataLabels: {
          enabled: true,
          enabledOnSeries: undefined,
          textAnchor: 'middle',
          distributed: false,
          offsetX: 0,
          offsetY: 0,
          style: {
            fontSize: '14px',
            fontFamily: 'Helvetica, Arial, sans-serif',
            fontWeight: 'bold',
            colors: ['#fff'],
          },
          background: {
            enabled: true,
            foreColor: '#000',
            padding: 4,
            borderRadius: 2,
            borderWidth: 1,
            borderColor: '#fff',
            opacity: 0.9,
            dropShadow: {
              enabled: false,
              top: 1,
              left: 1,
              blur: 1,
              color: '#000',
              opacity: 0.45,
            },
          },
          dropShadow: {
            enabled: false,
            top: 1,
            left: 1,
            blur: 1,
            color: '#000',
            opacity: 0.45,
          },
        },
      };
    },
    series() {
      return [
        {
          name: this.leftDate,
          data: this.LeftTransfromed,
        },
        {
          name: this.rightDate,
          data: this.RightTransformed,
        },
      ];
    },
    fields() {
      const fieldData = [
        { key: 'Asset', label: this.selectedAsset, sortable: true },
        { key: 'Left Data', label: this.leftDate, sortable: true },
        { key: 'Right Data', label: this.rightDate, sortable: true },
        { key: 'Progress', label: 'Progress (%)', sortable: true },
      ];
      return fieldData;
    },
    paginatedData() {
      const start = (this.currentPage - 1) * this.perPage;
      const end = start + this.perPage;
      return this.tableData.slice(start, end);
    },
    tableData() {
      const tableData = [];
      for (var i = 0; i < this.labels.length; i++) {
        const leftData = parseInt(this.LeftTransfromed[i] || 0);
        const rightData = parseInt(this.RightTransformed[i] || 0);
        const progress =
          leftData > rightData
            ? ((leftData - rightData) / leftData) * 100
            : ((rightData - leftData) / rightData) * 100;
        const progressValue = isNaN(progress) ? 0.0 : progress;
        tableData.push({
          Asset: this.labels[i],
          'Left Data': leftData,
          'Right Data': rightData,
          Progress: progressValue.toFixed(2),
        });
      }
      return tableData;
    },
  },
  methods: {
    getLables() {
      if (this.projectType == SOLAR_TYPE.SCPM) {
        this.labels = [
          ...new Set([...Object.keys(this.summaryLeft), ...Object.keys(this.summaryRight)]),
        ];
        this.labelsObjLeft = this.summaryLeft;
        this.labelsObjRight = this.summaryRight;
      } else if (this.projectType == SOLAR_TYPE.SCQM) {
        this.labels = [
          ...new Set([...Object.keys(this.deviationLeft), ...Object.keys(this.deviationRight)]),
        ];
        this.labelsObjLeft = this.deviationLeft;
        this.labelsObjRight = this.deviationRight;
      }
      this.getParentCategory();
    },
    getParentCategory() {
      if (this.labels && this.labelsObjLeft && this.labelsObjRight) {
        const firstKey = this.labels[0];
        if (firstKey) {
          this.parentCategory = [
            ...new Set([
              ...Object.keys(this.labelsObjLeft[firstKey]),
              ...Object.keys(this.labelsObjRight[firstKey]),
            ]),
          ];
          this.selectedAsset = this.parentCategory[0];
        }
      }
      this.getChildCategory();
    },
    getChildCategory() {
      if (this.parentCategory) {
        const firstKey = this.labels[0];
        let child = '';
        try {
          child = this.labelsObjLeft[firstKey][this.selectedAsset];
        } catch {
          child = this.labelsObjRight[firstKey][this.selectedAsset];
        }
        if (child) {
          this.childCategory = Object.keys(child);
          this.selectedFea = this.childCategory[0];
          this.getData();
        }
      }
    },
    getData() {
      this.summaryLeftData();
      this.summaryRightData();
    },
    async summaryLeftData() {
      if (this.projectType == SOLAR_TYPE.SCPM) {
        this.LeftTransfromed = await this.objectToArraySCPM(this.summaryLeft);
      } else if (this.projectType == SOLAR_TYPE.SCQM) {
        this.LeftTransfromed = await this.objectToArraySCQM(this.deviationLeft);
      }
      return [];
    },
    async summaryRightData() {
      if (this.projectType == SOLAR_TYPE.SCPM) {
        this.RightTransformed = await this.objectToArraySCPM(this.summaryRight);
      } else if (this.projectType == SOLAR_TYPE.SCQM) {
        this.RightTransformed = await this.objectToArraySCQM(this.deviationRight);
      }
      return [];
    },
    async objectToArraySCQM(obj) {
      const requiredArray = [];
      for (const inverter in obj) {
        try {
          requiredArray.push(obj[inverter][this.selectedAsset].actual);
        } catch {
          requiredArray.push(0);
        }
      }
      return await requiredArray;
    },
    async objectToArraySCPM(obj) {
      const requiredArray = [];
      for (const inverter in obj) {
        try {
          requiredArray.push(obj[inverter][this.selectedAsset][this.selectedFea].Actual);
        } catch {
          requiredArray.push(0);
        }
      }
      return await requiredArray;
    },
  },
  mounted() {
    this.getLables();
  },
};
</script>

<style scoped>
.compareDetailsContainer {
  display: flex;
  flex-direction: column;
  width: 100%;
}
</style>
