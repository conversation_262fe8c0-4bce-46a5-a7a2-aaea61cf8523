<template>
  <div class="nav-sticky">
    <b-navbar toggleable="lg" type="dark" variant="dark" class="py-1">
      <b-navbar-brand>
        <b-img :src="imageURL" alt="Datasee.AI" rounded class="navbar-image"></b-img>
      </b-navbar-brand>
      <b-navbar-toggle target="nav-collapse" />
      <b-collapse id="nav-collapse" is-nav style="padding: 0px !important">
        <b-navbar-nav class="ml-auto">
          <b-nav-item>
            <router-link to="/home" class="nav-link" active-class="active-link" exact>
              <i class="fa fa-home mr-1"></i> Home
            </router-link>
          </b-nav-item>
          <b-nav-item>
            <router-link to="/user-profile" class="nav-link" active-class="active-link" exact>
              <i class="fa fa-user mr-1"></i> My Profile
            </router-link>
          </b-nav-item>
          <b-nav-item v-if="isAdmin">
            <router-link to="/manage-users" class="nav-link" active-class="active-link" exact>
              <i class="fa fa-users mr-1"></i> Manage Users
            </router-link>
          </b-nav-item>
          <b-nav-item @click="helpCenterRedirect" class="nav-link" role="button">
            <i class="fa fa-clipboard-question mr-1"></i> Help Center
          </b-nav-item>
          <b-nav-item @click="logout" class="nav-link" role="button">
            <i class="fa fa-arrow-right-from-bracket mr-1"></i> Logout
          </b-nav-item>
        </b-navbar-nav>
      </b-collapse>
    </b-navbar>
  </div>
</template>

<script>
import keycloakService from '@/services/keycloakService';
import { mapGetters } from 'vuex';

export default {
  name: 'navbar',
  data() {
    return {
      imageURL: process.env.VUE_APP_LOGO_NAVBAR,
      helpCenter: process.env.VUE_APP_HELP_CENTER,
    };
  },
  computed: {
    ...mapGetters('userStore', ['isAdmin']),
  },
  methods: {
    logout() {
      this.$root.$emit('showToast', {
        message: '',
        title: 'Logging out...',
        variant: 'success',
      });
      keycloakService.logout();
      this.$store.dispatch('logout');
      this.$router.push('/login');
    },
    helpCenterRedirect() {
      window.open(this.helpCenter, '_blank');
    },
  },
};
</script>

<style scoped>
.navbar-image {
  width: 250px;
  height: 40px;
}
.nav-sticky {
  top: 0;
  position: sticky;
  z-index: 1000;
  background-color: #000;
}
.nav-link {
  cursor: pointer;
}
@media (max-width: 767px) {
  .navbar-image {
    width: 200px;
    height: 32px;
  }
  .navbar-toggler {
    padding: 4px 8px !important;
    border: 1px solid rgba(255, 255, 255, 0.3);
  }
  .navbar-collapse {
    margin-top: 8px;
    padding: 8px 0;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
  }
  .nav-link {
    padding: 10px 14px !important;
    border-radius: 6px;
    min-height: 44px;
    display: flex;
    align-items: center;
  }
  .nav-link:hover,
  .nav-link:focus {
    background-color: rgba(255, 255, 255, 0.1);
  }
}
@media (max-width: 370px) {
  .navbar-image {
    width: 180px;
    height: 28px;
  }
  .navbar-toggler {
    padding: 2px 6px !important;
  }
  .nav-link {
    font-size: 14px;
    padding: 10px 12px !important;
  }
}
</style>
