<template>
  <div>
    <div class="text-center my-2" v-if="select.selected.length > 0 && select.options.length > 0">
      <b-dropdown :text="dropdownText" class="project-select w-100">
        <b-dropdown-item-button @click="selectAll">
          <i class="fas fa-check-square mr-2"></i>Select All
        </b-dropdown-item-button>
        <b-dropdown-item-button @click="clearAll">
          <i class="fas fa-times mr-2"></i>Clear All
        </b-dropdown-item-button>
        <b-dropdown-divider></b-dropdown-divider>
        <div class="px-2" style="max-height: 200px; overflow-y: auto; min-width: 150px">
          <b-form-checkbox-group
            v-model="select.selected"
            :options="checkboxOptions"
            @change="inverterPageChange"
            stacked>
          </b-form-checkbox-group>
        </div>
      </b-dropdown>
      <div class="mt-2 text-sm text-muted">
        Selected: {{ select.selected.length }} of {{ select.options.length }} inverters
      </div>
    </div>
    <ng-container v-if="select.selected.length > 0 && Object.keys(combinedInverterData).length > 0">
      <b-list-group>
        <b-list-group-item v-for="(itemL1, keyL1, indexL1) in combinedInverterData" :key="keyL1">
          <div class="sideBarList" :key="currentInverter">
            <div class="d-flex flex-row justify-content-between">
              <span>
                <i
                  @click="lightning(keyL1, itemL1)"
                  :id="'eye_' + keyL1"
                  :class="`far fa-eye-slash font_light mr-2`"></i>
              </span>
              <span v-b-toggle="`${currentInverter}${indexL1}L1`" class="text-capitalize">
                {{ keyL1 }}
              </span>
              <span><i class="fas fa-caret-down"></i></span>
            </div>
            <b-collapse
              v-if="Object.keys(itemL1).length >= 1"
              :id="`${currentInverter}${indexL1}L1`"
              class="mt-2">
              <div
                class="subItem1 pointer"
                v-for="(itemL2, keyL2, indexL2) in itemL1"
                :key="`${keyL2}-${indexL2}`"
                :class="{ active: clickedSubLink === itemL2 }">
                <div v-b-toggle="`${currentInverter}${indexL1}${indexL2}L2`">
                  <div
                    class="sublink d-flex justify-content-between align-items-center"
                    @click="renderDetails(keyL1, keyL2, itemL2)">
                    <span
                      v-b-tooltip.hover.left.html="
                        `<b>Actual</b>: ${itemL2.Actual} <br /> <b>Planned</b>: ${itemL2.Total}`
                      ">
                      {{ keyL2 }}
                    </span>
                    <span class="d-flex align-items-center ml-3">
                      {{ itemL2.Actual }}
                      <span :style="{ color: itemL2.color }" class="fas fa-circle float-right ml-3">
                      </span>
                    </span>
                  </div>
                </div>
              </div>
            </b-collapse>
          </div>
        </b-list-group-item>
      </b-list-group>

      <div v-if="clickedDetailsVisible" class="sideBarDetailsContainer">
        <div class="clickedHeader d-flex justify-content-between px-2">
          <span>{{ kmlheading }}</span>
          <span>
            <i class="icon fa fa-times" @click="closeDetails()"></i>
          </span>
        </div>
        <div class="clickedDetails d-flex flex-row align-items-center justify-content-between">
          <div class="w-75">
            <b>Actual:</b> {{ clickedDetails.Actual }} <br />
            <b>Planned:</b> {{ clickedDetails.Total }}
          </div>
          <div class="w-25">
            <apexchart
              type="radialBar"
              height="100"
              :options="options"
              :series="series"></apexchart>
          </div>
        </div>
      </div>
    </ng-container>
  </div>
</template>

<script>
export default {
  name: 'inverterSideNav',
  props: ['data'],
  data() {
    return {
      clickedDetailsVisible: false,
      clickedDetails: {},
      clickedInverter: null,
      actual: {},
      total: {},
      clickedSubLink: '',
      temp2: '',
      inverterCurrentPage: this.data.inverters[0],
      total_percentage: 0,
      kmlheading: '',
      select: {
        selected: [this.data.inverters[0]],
        options: this.data.inverters,
      },
      aggregatedData: {},
    };
  },
  computed: {
    kmlBaseUrl() {
      return this.$store.state.homeStore.projectData.currentDateProject.properties.kml ?? '';
    },
    series() {
      return [parseInt(this.total_percentage.toFixed())];
    },
    options() {
      return {
        plotOptions: {
          radialBar: {
            dataLabels: {
              show: true,
              name: {
                show: false,
              },
              value: {
                show: true,
                offsetY: 8,
              },
            },
            hollow: {
              size: '40%',
            },
          },
        },
      };
    },
    currentInverter() {
      this.$root.$emit('removeKml');
      return this.inverterCurrentPage ?? {};
    },
    checkboxOptions() {
      return this.select.options.map(option => ({
        text: option,
        value: option,
      }));
    },
    dropdownText() {
      if (this.select.selected.length === 0) {
        return 'Select Inverters';
      } else if (this.select.selected.length === 1) {
        return this.select.selected[0];
      } else if (this.select.selected.length === this.select.options.length) {
        return 'All Inverters Selected';
      } else {
        return `${this.select.selected.length} Inverters Selected`;
      }
    },
    combinedInverterData() {
      if (this.select.selected.length === 0) return {};

      const combined = {};

      this.select.selected.forEach(inverterName => {
        const inverterData = this.data.test[inverterName];
        if (inverterData) {
          Object.keys(inverterData).forEach(keyL1 => {
            if (!combined[keyL1]) {
              combined[keyL1] = {};
            }
            Object.keys(inverterData[keyL1]).forEach(keyL2 => {
              const item = inverterData[keyL1][keyL2];
              if (!combined[keyL1][keyL2]) {
                combined[keyL1][keyL2] = {
                  Actual: 0,
                  Total: 0,
                  color: item.color,
                  kml: [],
                  blankarea_kml: [],
                  blankarea_color: item.blankarea_color,
                };
              }
              combined[keyL1][keyL2].Actual += parseFloat(item.Actual || 0);
              combined[keyL1][keyL2].Total += parseFloat(item.Total || 0);
              if (item.kml) {
                const kmlItems = Array.isArray(item.kml) ? item.kml : [item.kml];
                combined[keyL1][keyL2].kml.push(...kmlItems.map(kml => `${inverterName}/${kml}`));
              }
              if (item.blankarea_kml) {
                const blankareaKmlItems = Array.isArray(item.blankarea_kml)
                  ? item.blankarea_kml
                  : [item.blankarea_kml];
                combined[keyL1][keyL2].blankarea_kml.push(
                  ...blankareaKmlItems.map(kml => `${inverterName}/${kml}`)
                );
              }
            });
          });
        }
      });
      return combined;
    },
  },
  methods: {
    selectAll() {
      this.select.selected = [...this.select.options];
    },
    clearAll() {
      this.select.selected = [];
    },
    lightning(keyL1, i) {
      this.$root.$emit('removeKml');
      const eyeIcon = document.getElementById('eye_' + keyL1);
      if (keyL1 === this.temp2) {
        eyeIcon.className = 'far fa-eye-slash';
        this.temp2 = '';
      } else {
        const eyeElements = document.querySelectorAll('[id^="eye_"]');
        eyeElements.forEach(element => {
          element.className = 'far fa-eye-slash';
        });
        eyeIcon.className = 'far fa-eye';
        for (const n in i) {
          const kmlArray = i[n].kml || [];
          const color = i[n].color || 'rgb(26, 25, 23)';
          const rgb = color
            .replace(/[^\d,]/g, '')
            .split(',')
            .map(Number);
          const hex_color = `#${rgb.map(c => c.toString(16).padStart(2, '0')).join('')}`;
          kmlArray.forEach(kml => {
            this.$root.$emit('kmlClicked', {
              url: `${this.kmlBaseUrl}inverter/${kml}`,
              hex: hex_color,
              is_polyline: true,
            });
          });
        }
        this.temp2 = keyL1;
      }
    },
    renderDetails(keyL2, keyL3, value) {
      this.$root.$emit('removeKml');
      this.clickedDetailsVisible = true;
      this.clickedInverter = keyL2 + keyL3;
      this.clickedDetails = value;
      this.kmlheading = `${keyL3} (${this.select.selected.length} inverters)`;
      this.clickedSubLink = value;
      this.total_percentage = (this.clickedDetails.Actual / this.clickedDetails.Total) * 100;
      if (value.Actual || value.Total || value.Actual === 0 || value.Total === 0) {
        this.Actual = value.Actual;
        this.Total = value.Total;
      } else {
        this.Actual = value.Actual_value;
        this.Total = value.Current_value;
      }
      const rgb = value.color
        .replace(/[^\d,]/g, '')
        .split(',')
        .map(Number);
      const hex_color = `#${rgb.map(c => c.toString(16).padStart(2, '0')).join('')}`;
      if (value && value.kml) {
        const kmlItems = Array.isArray(value.kml) ? value.kml : [value.kml];
        for (const kml of kmlItems) {
          this.$root.$emit('kmlClicked', {
            url: `${this.kmlBaseUrl}inverter/${kml}`,
            hex: hex_color,
            is_polyline: true,
          });
        }
        if (value.blankarea_kml) {
          const blankareaKmltems = Array.isArray(value.blankarea_kml)
            ? value.blankarea_kml
            : [value.blankarea_kml];
          for (const kml of blankareaKmltems) {
            this.$root.$emit('kmlClicked', {
              url: `${this.kmlBaseUrl}inverter/${kml}`,
              hex: value?.blankarea_color || '#696969',
              is_polyline: true,
            });
          }
        }
      }
    },
    closeDetails() {
      this.clickedDetails = {};
      this.clickedDetailsVisible = false;
      this.$root.$emit('removeKml');
    },
    inverterPageChange(selectedInverters) {
      this.inverterCurrentPage =
        selectedInverters.length > 0 ? selectedInverters[0] : this.data.inverters[0];
      this.clickedDetailsVisible = false;
      this.clickedDetails = {};
      this.$root.$emit('removeKml');
    },
  },
  unmounted() {
    this.$root.$emit('removeKml');
  },
};
</script>

<style scoped>
.active {
  color: var(--primary) !important;
}
</style>
