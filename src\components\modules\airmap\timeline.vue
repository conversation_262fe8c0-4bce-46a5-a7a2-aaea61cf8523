<template>
  <div>
    <div class="edit_date" @click="date_format">
      <i class="fa fa-refresh"></i>
    </div>
    <b-dropdown :text="formattedCurrentDate">
      <b-dropdown-item
        v-for="(date, index) in datesArray"
        :key="index"
        @click="switchDate(date)"
        :class="{ selectedDate: date === currentDateProject.date }">
        <span>{{ formatDisplayDate(date) }}</span>
      </b-dropdown-item>
    </b-dropdown>
  </div>
</template>

<script>
export default {
  name: 'Timeline',
  data() {
    return {
      dateFormatType: '',
      formatCounter: 0,
    };
  },
  computed: {
    projectData() {
      return this.$store.state.homeStore.projectData;
    },
    currentDateProject() {
      return this.projectData.currentDateProject;
    },
    datesArray() {
      return this.projectData.date
        .filter(entry => entry.type === this.currentDateProject.type)
        .map(entry => entry.date);
    },
    formattedCurrentDate() {
      return this.formatDisplayDate(this.currentDateProject.date);
    },
  },
  methods: {
    formatDisplayDate(date) {
      if (!date) return '';
      if (this.dateFormatType === 'changed') {
        return date.split('-').reverse().join('-');
      } else if (this.dateFormatType === 'changed1') {
        return this.format(date);
      }
      return date;
    },
    format(date) {
      const parts = date.split('-');
      parts.push(parts.shift());
      return parts.join('-');
    },
    date_format() {
      const date = this.currentDateProject.date;
      if (!date) return;

      if (this.formatCounter === 0) {
        this.dateFormatType = 'changed';
      } else if (this.formatCounter === 1) {
        this.dateFormatType = 'changed1';
      } else {
        this.dateFormatType = '';
      }
      this.formatCounter = (this.formatCounter + 1) % 3;
    },
    switchDate(date) {
      if (!date) return;
      this.$store.dispatch('homeStore/switchProjectbyDate', date);
      this.$store.dispatch('airmapStore/getAllAoi', false);
      // this.$store.dispatch('airmapStore/getAllActivity');
      // this.$store.dispatch('airmapStore/getAllCadApi');

      this.$root.$emit('closeSideBar');
      this.$root.$emit('removeKml');
      this.$root.$emit('initializeMap');
    },
  },
  mounted() {
    this.switchDate(this.currentDateProject.date);

    this.$nextTick(() => {
      const dropdownMenu = this.$el.querySelector('.dropdown-menu');
      if (dropdownMenu) {
        dropdownMenu.addEventListener('wheel', e => e.stopPropagation());
      }
    });
  },
};
</script>

<style scoped>
.edit_date {
  border-radius: 5px;
  padding: 6.8px;
  margin-left: -22px;
  position: absolute;
  background-color: var(--dark);
  z-index: 999;
  color: var(--transparent-white);
  cursor: pointer;
}
</style>
