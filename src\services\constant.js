export const SESSION_TIMEOUT = 2 * 3600000; // Session timeout: 2 hours in milliseconds
export const REFRESH_INTERVAL = 240 * 1000; // 4 minutes

export const STORAGE_KEYS = {
  TOKEN_REFRESH: 'tokenRefreshTime',
  CUSTOM_TOKEN: 'customToken',
  KEY_KEY: 'key',
  IV_KEY: 'iv',
};

export const CATEGORY = {
  SOLAR: 'solar',
  CONSTRUCTION: 'construction',
};

export const SOLAR_TYPE = {
  SCPM: 'SCPM',
  SCQM: 'SCQM',
};

export const CHART_TYPES = {
  LINE: 'line',
  STACKED: 'stacked',
};

export const VIEWS = {
  GLOBAL: 'global',
  INVERTER: 'inverter',
};

export const CHART_COLORS = {
  ACTUAL: '#1f77b4',
  TARGET: '#ff7f0e',
};

export const formatCustomDate = date => {
  const formatter = new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    hour12: true,
  });
  const parsedDate = new Date(date);
  const parts = formatter.formatToParts(parsedDate);

  const day = parts.find(part => part.type === 'day').value;
  const month = parts.find(part => part.type === 'month').value;
  const year = parts.find(part => part.type === 'year').value;
  const hour = parts.find(part => part.type === 'hour').value;
  const minute = parts.find(part => part.type === 'minute').value;
  const second = parts.find(part => part.type === 'second').value;
  const period = parts.find(part => part.type === 'dayPeriod').value;

  return `${day}-${month}-${year}, ${hour}:${minute}:${second} ${period}`;
};

export function formatProjectName(name) {
  return name?.toUpperCase().replace(/[_-]/g, ' ');
}

export const handleResponse = response => {
  const { data } = response;
  if (data && data.status === 'success') {
    return data;
  }
  throw new Error(data.message || 'An error occurred');
};

export const handleError = error => {
  console.error('API Error:', error);
  return Promise.reject(error.response?.data || { message: 'Unknown error occurred' });
};
