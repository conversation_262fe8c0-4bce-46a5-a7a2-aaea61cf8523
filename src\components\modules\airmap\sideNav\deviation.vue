<template>
  <div>
    <ng-container v-if="isbetaSCQM">
      <b-list-group>
        <b-list-group-item v-for="(itemL1, keyL1, indexL1) in data" :key="indexL1">
          <div class="sideBarList">
            <div class="d-flex flex-row justify-content-between">
              <span></span>
              <span v-b-toggle="`summary${indexL1}L1`" class="text-capitalize">
                {{ keyL1 }} - {{ itemL1.summation }}
              </span>
              <span><i class="fas fa-caret-down"></i></span>
            </div>
            <b-collapse v-if="Object.keys(itemL1).length >= 1" :id="`summary${indexL1}L1`">
              <div
                class="subItem1 pointer"
                id="subItem1"
                v-for="(itemL2, keyL2, indexL2) in itemL1.properties"
                :key="`${keyL2}-${indexL2}`"
                :class="{ active: clickedDetails === itemL2 }">
                <div
                  class="sublink d-flex justify-content-between align-items-center"
                  @click="renderDetails(keyL1, keyL2, itemL2)">
                  <span>{{ keyL2 }}</span>
                  <span class="d-flex align-items-center ml-3">
                    {{ itemL2.count }}
                    <span :style="{ color: itemL2.color }" class="fas fa-circle float-right ml-3">
                    </span>
                  </span>
                </div>
              </div>
            </b-collapse>
          </div>
        </b-list-group-item>
      </b-list-group>
    </ng-container>
    <ng-container v-else>
      <div
        class="sidenavListItem"
        @click="renderDetails(index, key, asset)"
        v-for="(asset, key, index) in data"
        :key="index">
        <div>
          <div v-b-toggle="`dev${index}`">
            <span v-bind:class="{ active: clickedDetails === asset }">{{ key }}</span>
            <i
              :style="{ color: `rgb(${String(asset.color)})` }"
              class="fas fa-circle float-right"></i>
          </div>
        </div>
      </div>
    </ng-container>

    <div v-if="clickedDetailsVisible" class="sideBarDetailsContainer">
      <div class="clickedHeader d-flex justify-content-between px-2">
        <span>{{ kmlheading }}</span>
        <span>
          <i class="icon fa fa-times" @click="closeDetails()"></i>
        </span>
      </div>
      <div class="clickedDetails d-flex flex-row align-items-center justify-content-between">
        <div class="w-100">
          <div
            v-for="(item3, key3, index3) in clickedDetails.properties"
            :key="`${key3}-${index3}`"
            class="d-flex flex-row justify-content-between mt-2">
            <span class="w-25">{{ key3 }} :</span>
            <span class="w-75">
              <b>{{ item3 }}</b>
            </span>
          </div>
        </div>
      </div>
    </div>

    <div class="text-right down_btn">
      <b-button class="download-csv" @click="show" v-b-tooltip.hover title="Reference">
        <i class="icon fa fa-info-circle"></i>
      </b-button>
    </div>
    <carouselModel />
  </div>
</template>

<script>
const carouselModel = () => import('../carouselModel.vue');

export default {
  name: 'deviation',
  components: {
    carouselModel,
  },
  props: ['data'],
  data() {
    return {
      clickedDetailsVisible: false,
      clickedDetails: {},
      kmlheading: '',
      temp2: '',
    };
  },
  computed: {
    isbetaSCQM() {
      return this.$store.state.userStore.loggedInUser.is_beta_scqm;
    },
    kmlBaseUrl() {
      return this.$store.state.homeStore.projectData.currentDateProject.properties.kml ?? '';
    },
  },
  methods: {
    show() {
      this.$bvModal.show('pop-carousel');
    },
    rgbToHex(r, g, b) {
      return `#${((r << 16) | (g << 8) | b).toString(16).padStart(6, '0')}`;
    },
    renderDetails(keyL2, keyL3, value) {
      if (!this.isbetaSCQM) {
        this.clickedDetailsVisible = true;
      }
      this.$root.$emit('removeKml');
      let hex_color, kml_url;
      this.clickedDetails = value;
      this.kmlheading = keyL3;
      this.total_percentage =
        this.clickedDetails.Total == 'NA'
          ? 0
          : (this.clickedDetails.Actual / this.clickedDetails.Total) * 100;
      this.temp1 = keyL3;
      if (value && value.kml) {
        if (this.isbetaSCQM) {
          const rgb = value.color
            .replace(/[^\d,]/g, '')
            .split(',')
            .map(Number);
          hex_color = `#${rgb.map(c => c.toString(16).padStart(2, '0')).join('')}`;
          kml_url = `${this.kmlBaseUrl}GLOBAL/deviation/${value.kml}`;
        } else {
          hex_color = this.rgbToHex(value.color[0], value.color[1], value.color[2]);
          kml_url = `${this.kmlBaseUrl}GLOBAL/deviation/${value.kml[0]}.kml`;
        }
        this.$root.$emit('kmlClicked', {
          url: kml_url,
          hex: hex_color,
          is_polyline: 'is_polyline' in value,
        });
      }
    },
    closeDetails() {
      this.clickedDetails = {};
      this.clickedDetailsVisible = false;
      this.$root.$emit('removeKml');
    },
  },
  unmounted() {
    this.$root.$emit('removeKml');
  },
};
</script>

<style scoped>
.active {
  color: var(--primary) !important;
}
</style>
