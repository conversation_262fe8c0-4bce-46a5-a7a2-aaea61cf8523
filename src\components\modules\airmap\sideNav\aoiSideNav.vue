<template>
  <div>
    <div class="sidenavListContainer">
      <div class="text-center create-btn">
        <b-button
          pill
          class="primaryBg mt-2 border-0"
          @click="createNewAoi()"
          v-b-tooltip.hover.left
          title="Create AOI">
          <i class="fa fa-plus"></i>
        </b-button>
      </div>
      <div
        class="d-flex justify-content-between sidenavListItem"
        :class="{
          clickedHighlighter: clickedItemKey === currentDate + aoiItem.label,
        }"
        @click="aoiItemClicked(aoiItem)"
        v-for="(aoiItem, key, index) in data"
        :key="index">
        <div>
          <span class="ml-2">{{ aoiItem.label }}</span>
        </div>
      </div>
    </div>
    <div v-if="clickedDetailsVisible" class="d-flex flex-column sideBarDetailsContainer">
      <div class="d-flex justify-content-between align-items-center bg-dark rounded-top">
        <b class="ml-2 text-light">{{ clickedDetails.label }}</b>
        <div class="d-flex align-items-center justify-content-between" style="width: 40%">
          <div
            role="button"
            class="text-light p-2 m-1"
            v-b-tooltip.hover
            title="Edit AOI"
            @click="showEditModal(clickedDetails)">
            <i class="fa fa-pen-to-square"></i>
          </div>
          <div
            role="button"
            class="text-light p-2"
            @click="downloadAoi(clickedDetails)"
            v-b-tooltip.hover
            title="Download AOI">
            <i class="fa fa-download"></i>
          </div>
          <div
            role="button"
            class="text-light p-2 m-1"
            v-b-tooltip.hover
            title="Delete AOI"
            @click="showDeleteModal(clickedDetails)">
            <i class="fa fa-trash"></i>
          </div>
          <div role="button" class="text-light p-2 m-1">
            <i class="icon fa fa-times" aria-controls="sidebar" @click="closeDetails()"></i>
          </div>
        </div>
      </div>
      <div class="sideBarDetails d-flex align-items-center rounded-bottom" style="min-width: 350px">
        <div class="w-100 p-2">
          <div v-if="showAreaOrDistance">
            <span class="font-weight-bold">{{ areaOrDistanceLabel }}:</span>
            {{ clickedDetails.area }} {{ areaOrDistanceUnit }} <br />
          </div>
          <div v-if="layerType === 'marker'">
            <span class="font-weight-bold">Latitude:</span> {{ clickedDetails.polygon.lat }}
            <br />
            <span class="font-weight-bold">Longitude:</span> {{ clickedDetails.polygon.lng }}
            <br />
          </div>
          <div>
            <span class="font-weight-bold">Description:</span> {{ clickedDetails.description }}
            <br />
            <span class="font-weight-bold">Created By:</span> {{ clickedDetails.created_by }}
            <br />
            <span class="font-weight-bold">Created On:</span>
            {{ dateFormat(clickedDetails.created_at) }}
          </div>
        </div>
      </div>
    </div>
    <b-modal
      id="deleteConfirmModal"
      title="Deletion Confirmation"
      :hide-footer="true"
      no-close-on-esc
      no-close-on-backdrop
      centered>
      <deleteConfirmModal :toBeDeleted="toBeDeleted" :id="toBeDeletedId" :deleteAPI="deleteAoi" />
    </b-modal>
  </div>
</template>

<script>
import { formatCustomDate } from '@/services/constant';
const deleteConfirmModal = () => import('@/components/shared/deleteConfirmModal.vue');

export default {
  name: 'aoiSideNav',
  components: { deleteConfirmModal },
  props: ['data'],
  data() {
    return {
      clickedDetailsVisible: false,
      layerType: false,
      clickedDetails: {},
      clickedItemKey: '',
      toBeDeleted: '',
      toBeDeletedId: '',
      deleteAoi: 'airmapStore/deleteAoi',
      callOnce: 1, // Due to the issue of v-b-tooltip calling destroy hooks many times
    };
  },
  computed: {
    currentDate() {
      return this.$store.state.airmapStore.currentDate;
    },
    dateFormat() {
      return originalDate => {
        return formatCustomDate(originalDate);
      };
    },
    showAreaOrDistance() {
      return ['polygon', 'rectangle', 'circle', 'polyline'].includes(this.layerType);
    },
    areaOrDistanceLabel() {
      if (this.layerType === 'polyline') {
        return 'Distance';
      }
      return 'Area';
    },
    areaOrDistanceUnit() {
      if (this.layerType === 'polygon' || this.layerType === 'rectangle') {
        return 'hectares';
      }
      return 'm';
    },
  },
  methods: {
    aoiItemClicked(aoi) {
      this.clickedItemKey = `${aoi.label}-${aoi.id}`;
      this.clickedDetails = aoi;
      this.layerType = aoi.aoi_type;
      this.clickedDetailsVisible = true;
      this.$root.$emit('aoiItemClicked', aoi);
    },
    closeDetails() {
      this.clickedDetails = {};
      this.clickedDetailsVisible = false;
      this.$root.$emit('aoiClosed');
    },
    showDeleteModal(aoi) {
      this.toBeDeleted = aoi.label;
      this.toBeDeletedId = aoi.id;
      this.$bvModal.show('deleteConfirmModal');
    },
    showEditModal(aoi) {
      this.$root.$emit('aoiEdited', aoi);
    },
    downloadAoi(aoi) {
      this.$root.$emit('aoiDownload', aoi);
    },
    createNewAoi() {
      this.$root.$emit('createNewAoi');
    },
    removeAoi(aoiLabel) {
      if (this.clickedItemKey === aoiLabel) {
        this.clickedDetails = {};
        this.clickedDetailsVisible = false;
      }
    },
  },
  mounted() {
    this.$root.$on('aoiDeleted', aoiLabel => {
      this.removeAoi(aoiLabel);
    });
    this.$root.$on('aoiCloseOnCreate', () => {
      this.clickedDetailsVisible = false;
    });
  },
  unmounted() {
    if (this.callOnce) {
      this.callOnce = null;
      this.$root.$emit('aoiClosed');
    }
  },
};
</script>

<style scoped></style>
