<template>
  <div>
    <div class="sidenavListContainer">
      <div>
        <div
          class="d-flex justify-content-between sidenavListItem"
          :class="{
            clickedHighlighter: clickedAsset === currentDate + key + index,
          }"
          @click="renderDetails(key, index, asset)"
          v-for="(asset, key, index) in assetData"
          :key="index">
          <div>
            {{ key.toLowerCase() }}
          </div>
          <div>
            <i
              :style="{ color: `rgb(${String(asset.color)})` }"
              class="fas fa-square float-right"></i>
          </div>
        </div>
      </div>

      <div v-if="clickedDetailsVisible" class="d-flex flex-column sideBarDetailsContainer">
        <div class="d-flex justify-content-between align-items-center bg-dark rounded-top">
          <b class="ml-2 text-light">{{ clickedDetails.title }}</b>
          <div role="button" class="mr-2 text-light closeIcon">
            <i class="icon fa fa-times" aria-controls="sidebar" @click="closeDetails()"></i>
          </div>
        </div>
        <div class="sideBarDetails d-flex align-items-center rounded-bottom py-2">
          <div class="w-75">
            <div class="d-flex justify-content-around">
              <span><b>Area</b></span>
              <span>{{ clickedDetails.Area_m2 }} m<sup>2</sup></span>
            </div>
            <div class="d-flex justify-content-around">
              <span><b>Length</b></span>
              <span>{{ clickedDetails.Length_m }} m</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'assetsSideNav',
  props: ['assetData'],
  data() {
    return {
      clickedDetailsVisible: false,
      clickedDetails: {},
      clickedAsset: null,
      temp1: '',
    };
  },
  computed: {
    currentDate() {
      return this.$store.state.airmapStore.currentDate ?? '';
    },
    kmlBaseUrl() {
      return this.$store.state.homeStore.projectProperties.kml ?? '';
    },
  },
  methods: {
    componentToHex(c) {
      const hex = c.toString(16);
      return hex.length == 1 ? '0' + hex : hex;
    },
    rgbToHex(r, g, b) {
      return '#' + this.componentToHex(r) + this.componentToHex(g) + this.componentToHex(b);
    },
    renderDetails(keyL2, keyL3, value) {
      this.clickedDetailsVisible = !this.clickedDetailsVisible;
      if (this.clickedDetailsVisible == false) {
        if (keyL3 == this.temp1) {
          this.clickedDetails = {};
          this.$root.$emit('removeKml');
        }
        if (keyL3 !== this.temp1) {
          this.clickedDetailsVisible = !this.clickedDetailsVisible;
          this.clickedAsset = this.currentDate + keyL2 + keyL3;
          this.clickedDetails = value;
          let title = value.KML.split('.')[0].toLowerCase(); //The data from backend is all caps.
          this.clickedDetails.title = title ? title : 'Details';
          this.clickedDetailsVisible = true;
          this.temp1 = keyL3;
        }
      } else {
        this.clickedAsset = this.currentDate + keyL2 + keyL3;
        this.clickedDetails = value;
        let title = value.KML.split('.')[0].toLowerCase(); //The data from backend is all caps.
        this.clickedDetails.title = title ? title : 'Details';
        this.clickedDetailsVisible = true;
        this.temp1 = keyL3;
      }
    },
    closeDetails() {
      this.clickedDetails = {};
      this.clickedDetailsVisible = false;
      this.$root.$emit('removeKml');
    },
  },
  unmounted() {
    this.$root.$emit('removeKml');
  },
};
</script>

<style scoped>
.down_btn button {
  width: calc(100% - 40px);
  border-radius: 0px !important;
}
.text-center.down_btn {
  position: absolute;
  width: 100%;
  bottom: 10px;
}
</style>
