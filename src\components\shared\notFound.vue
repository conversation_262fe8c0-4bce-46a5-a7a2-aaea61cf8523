<template>
  <div>
    <navbar />
    <div class="d-flex flex-column justify-content-center align-items-center mt-3">
      <b-img :src="not_found" alt="Page Not Found" rounded fluid></b-img>
      <h2 class="font-weight-bold mt-2">Page Not Found</h2>
    </div>
  </div>
</template>

<script>
const navbar = () => import('@/components/shared/navbar.vue');

export default {
  name: 'notFound',
  components: { navbar },
  data() {
    return {
      not_found: process.env.VUE_APP_NOT_FOUND,
    };
  },
};
</script>

<style></style>
