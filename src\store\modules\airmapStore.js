import { handleError, handleResponse } from '@/services/constant';
import httpClient from '@/services/httpClient';

const getInitialState = () => ({
  aoiArray: [],
  activityArray: [],
  cadArray: [],
  aoiById: {},
});

const state = getInitialState();

const getters = {
  getAoiArray: state => state.aoiArray,
  getActivityArray: state => state.activityArray,
  getCadArray: state => state.cadArray,
};

const actions = {
  async getAllAoi({ commit, dispatch, state, rootState }, dataUpdate) {
    const projectData = rootState.homeStore.projectData;
    if (!projectData || !projectData.id || !projectData.currentDateProject) {
      return Promise.resolve([]);
    }
    const cacheKey = `${projectData.id}_${projectData.currentDateProject.date}`;
    const cachedAoi = state.aoiById?.[cacheKey];
    if (!dataUpdate && cachedAoi) {
      commit('setAllAoi', cachedAoi);
      return Promise.resolve({ status: 'success', data: cachedAoi });
    }
    dispatch('toggleSpinner', null, { root: true });
    try {
      const response = await httpClient.get(
        `drawtool/get_data/${projectData.id}/${projectData.currentDateProject.date}`
      );
      const aoiData = response?.data?.data;
      commit('cacheAoiById', { id: cacheKey, data: aoiData });
      commit('setAllAoi', aoiData);
      return handleResponse(response);
    } catch (error) {
      return handleError(error);
    } finally {
      dispatch('toggleSpinner', null, { root: true });
    }
  },
  async saveAoi({ dispatch }, data) {
    dispatch('toggleSpinner', null, { root: true });
    try {
      const response = await httpClient.post('drawtool/save', data);
      await dispatch('getAllAoi', true);
      return handleResponse(response);
    } catch (error) {
      return handleError(error);
    } finally {
      dispatch('toggleSpinner', null, { root: true });
    }
  },
  async updateAoi({ commit, dispatch }, data) {
    if (!data.id) {
      throw new Error('AOI ID is required for update');
    }
    dispatch('toggleSpinner', null, { root: true });
    try {
      const response = await httpClient.put(`drawtool/update/${data.id}`, data);
      commit('removeAoiById', data.id);
      await dispatch('getAllAoi', true);
      return handleResponse(response);
    } catch (error) {
      return handleError(error);
    } finally {
      dispatch('toggleSpinner', null, { root: true });
    }
  },
  async deleteAoi({ commit, dispatch }, id) {
    if (!id) {
      throw new Error('AOI ID is required for deletion');
    }
    dispatch('toggleSpinner', null, { root: true });
    try {
      const response = await httpClient.delete(`drawtool/delete/${id}`);
      commit('removeAoiById', id);
      await dispatch('getAllAoi', true);
      return handleResponse(response);
    } catch (error) {
      return handleError(error);
    } finally {
      dispatch('toggleSpinner', null, { root: true });
    }
  },
  // Uncomment and adjust as needed
  // async getAllActivity({ commit, dispatch, rootState }) {
  //   const projectId = rootState.homeStore.projectId;
  //   if (!projectId) throw new Error('Project ID is required');
  //   dispatch('toggleSpinner', null, { root: true });
  //   try {
  //     const response = await httpClient.get(`activity/get/${projectId}/`);
  //     commit('setAllActivity', response?.data?.data);
  //     return handleResponse(response);
  //   } catch (error) {
  //     return handleError(error);
  //   } finally {
  //     dispatch('toggleSpinner', null, { root: true });
  //   }
  // },
  // async createNewActivity({ dispatch, rootState }, activityData) {
  //   const projectId = rootState.homeStore.projectId;
  //   if (!projectId) throw new Error('Project ID is required');
  //   dispatch('toggleSpinner', null, { root: true });
  //   try {
  //     const response = await httpClient.post(`activity/add/${projectId}/`, activityData);
  //     await dispatch('getAllActivity');
  //     return handleResponse(response);
  //   } catch (error) {
  //     return handleError(error);
  //   } finally {
  //     dispatch('toggleSpinner', null, { root: true });
  //   }
  // },
  // async deleteActivity({ dispatch }, id) {
  //   if (!id) throw new Error('Activity ID is required for deletion');
  //   dispatch('toggleSpinner', null, { root: true });
  //   try {
  //     const response = await httpClient.delete(`activity/delete/${id}/`);
  //     await dispatch('getAllActivity');
  //     return handleResponse(response);
  //   } catch (error) {
  //     return handleError(error);
  //   } finally {
  //     dispatch('toggleSpinner', null, { root: true });
  //   }
  // },
};

const mutations = {
  resetModule(state) {
    Object.assign(state, getInitialState());
  },
  setAllAoi(state, data) {
    state.aoiArray = data;
  },
  setAllActivity(state, data) {
    state.activityArray = data;
  },
  setAllCad(state, data) {
    state.cadArray = data;
  },
  cacheAoiById(state, { id, data }) {
    state.aoiById = { ...state.aoiById, [id]: data };
  },
  removeAoiById(state, id) {
    delete state.aoiById[id];
  },
};

export default {
  namespaced: true,
  state,
  getters,
  actions,
  mutations,
};
