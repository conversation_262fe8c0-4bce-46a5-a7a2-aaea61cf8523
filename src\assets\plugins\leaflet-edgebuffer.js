!function(e,i){"function"==typeof define&&define.amd?define(["leaflet"],e):"object"==typeof exports&&(module.exports=e(require("leaflet"))),void 0!==i&&i.L&&!i.L.EdgeBuffer&&e(i.L)}((function(e){e.<PERSON>Buffer={previousMethods:{getTiledPixelBounds:e.GridLayer.prototype._getTiledPixelBounds}},e.GridLayer.include({_getTiledPixelBounds:function(i,t,o){var d=e.EdgeBuffer.previousMethods.getTiledPixelBounds.call(this,i,t,o),l=1;if(void 0!==this.options.edgeBufferTiles&&null!==this.options.edgeBufferTiles&&(l=this.options.edgeBufferTiles),l>0){var n=e.GridLayer.prototype.getTileSize.call(this).multiplyBy(l);d=new e.Bounds(d.min.subtract(n),d.max.add(n))}return d}})}),window);