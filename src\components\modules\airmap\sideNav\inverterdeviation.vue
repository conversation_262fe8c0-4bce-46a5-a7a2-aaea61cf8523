<template>
  <div>
    <div class="text-center my-2" v-if="select.selected.length > 0 && select.options.length > 0">
      <b-dropdown :text="dropdownText" class="project-select w-100">
        <b-dropdown-item-button @click="selectAll">
          <i class="fas fa-check-square mr-2"></i>Select All
        </b-dropdown-item-button>
        <b-dropdown-item-button @click="clearAll">
          <i class="fas fa-times mr-2"></i>Clear All
        </b-dropdown-item-button>
        <b-dropdown-divider></b-dropdown-divider>
        <div class="px-2" style="max-height: 200px; overflow-y: auto; min-width: 150px">
          <b-form-checkbox-group
            v-model="select.selected"
            :options="checkboxOptions"
            @change="inverterPageChange"
            stacked>
          </b-form-checkbox-group>
        </div>
      </b-dropdown>
      <div class="mt-2 text-sm text-muted">
        Selected: {{ select.selected.length }} of {{ select.options.length }} inverters
      </div>
    </div>

    <ng-container v-if="select.selected.length > 0 && Object.keys(combinedInverterData).length > 0">
      <div v-if="isbetaSCQM">
        <b-list-group>
          <b-list-group-item v-for="(itemL1, keyL1, indexL1) in combinedInverterData" :key="keyL1">
            <div class="sideBarList" :key="currentInverter">
              <div class="d-flex flex-row justify-content-between">
                <span></span>
                <span v-b-toggle="`${currentInverter}${indexL1}L1`" class="text-capitalize">
                  {{ keyL1 }} - {{ itemL1.total }}
                </span>
                <span><i class="fas fa-caret-down"></i></span>
              </div>
              <b-collapse
                v-if="Object.keys(itemL1).length >= 1"
                :id="`${currentInverter}${indexL1}L1`"
                class="mt-2">
                <div
                  class="subItem1 pointer"
                  v-for="(itemL2, keyL2, indexL2) in itemL1.properties"
                  :key="`${keyL2}-${indexL2}`"
                  :class="{ active: clickedSubLink === itemL2 }">
                  <div v-b-toggle="`${currentInverter}${indexL1}${indexL2}L2`">
                    <span
                      class="d-flex justify-content-between"
                      @click="renderDetails(keyL1, keyL2, itemL2)">
                      {{ keyL2 }}
                      <span class="d-flex align-items-center ml-3">
                        {{ itemL2.count }}
                        <span
                          :style="{ color: itemL2.color }"
                          class="fas fa-circle float-right ml-3">
                        </span>
                      </span>
                    </span>
                  </div>
                </div>
              </b-collapse>
            </div>
          </b-list-group-item>
        </b-list-group>
      </div>
      <div v-else>
        <div class="inverterListContainer">
          <div>
            <div
              class="sidenavListItem"
              @click="renderDetails(index, key, asset)"
              v-for="(asset, key, index) in combinedInverterData"
              :key="index">
              <div>
                <div v-b-toggle="`dev${index}`" v-bind:class="{ active: clickedDetails === asset }">
                  {{ key }}
                  <i
                    :style="{ color: `rgb(${String(asset.color)})` }"
                    class="fas fa-circle float-right"></i>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div v-if="clickedDetailsVisible" class="sideBarDetailsContainer">
          <div class="clickedHeader">
            <span class="clickedTitle">{{ kmlheading }}</span>
            <span class="closeIcon">
              <i @click="showInfo" class="fas fa-info-circle icon-color pr-3"></i>
              <i class="icon fa fa-times" aria-controls="sidebar" @click="closeDetails()"></i>
            </span>
          </div>
          <div class="clickedDetails">
            <b-list-group-item>
              <div
                v-for="(item3, key3, index3) in clickedDetails.properties"
                :key="`${key3}-${index3}`">
                {{ key3 }} :
                <span class="float-right">{{ item3 }}</span>
              </div>
            </b-list-group-item>
          </div>
        </div>
      </div>
      <div class="text-right down_btn">
        <b-button class="download-csv" @click="showInfo" v-b-tooltip.hover title="Reference">
          <i class="icon fa fa-info-circle"></i>
        </b-button>
      </div>
      <carouselModel />
    </ng-container>
  </div>
</template>

<script>
const carouselModel = () => import('../carouselModel.vue');

export default {
  name: 'inverterdeviation',
  components: {
    carouselModel,
  },
  props: ['data'],
  data() {
    return {
      total_percentage: Number,
      clickedSubLink: '',
      clickedDetailsVisible: false,
      clickedDetails: {},
      clickedInverter: null,
      kmlheading: '',
      inverterCurrentPage: this.data.inverters[0],
      select: {
        selected: [this.data.inverters[0]],
        options: this.data.inverters,
      },
      aggregatedData: {},
    };
  },
  computed: {
    isbetaSCQM() {
      return this.$store.state.userStore.loggedInUser.is_beta_scqm;
    },
    kmlBaseUrl() {
      return this.$store.state.homeStore.projectData.currentDateProject.properties.kml ?? '';
    },
    currentInverter() {
      return this.inverterCurrentPage ?? {};
    },
    checkboxOptions() {
      return this.select.options.map(option => ({
        text: option,
        value: option,
      }));
    },
    dropdownText() {
      if (this.select.selected.length === 0) {
        return 'Select Inverters';
      } else if (this.select.selected.length === 1) {
        return this.select.selected[0];
      } else if (this.select.selected.length === this.select.options.length) {
        return 'All Inverters Selected';
      } else {
        return `${this.select.selected.length} Inverters Selected`;
      }
    },
    combinedInverterData() {
      if (this.select.selected.length === 0) return {};
      const combined = {};
      this.select.selected.forEach(inverterName => {
        const inverterData = this.data.test[inverterName];
        if (inverterData) {
          if (this.isbetaSCQM) {
            Object.keys(inverterData).forEach(keyL1 => {
              if (!combined[keyL1]) {
                combined[keyL1] = {
                  total: 0,
                  properties: {},
                };
              }
              combined[keyL1].total += parseFloat(inverterData[keyL1].total || 0);
              if (inverterData[keyL1].properties) {
                Object.keys(inverterData[keyL1].properties).forEach(keyL2 => {
                  const item = inverterData[keyL1].properties[keyL2];
                  if (!combined[keyL1].properties[keyL2]) {
                    combined[keyL1].properties[keyL2] = {
                      count: 0,
                      color: item.color,
                      kml: [],
                      is_polyline: 'is_polyline' in item,
                    };
                  }
                  combined[keyL1].properties[keyL2].count += parseFloat(item.count || 0);
                  if (item.kml) {
                    const kmlItems = Array.isArray(item.kml) ? item.kml : [item.kml];
                    combined[keyL1].properties[keyL2].kml.push(
                      ...kmlItems.map(kml => `${inverterName}/${kml}`)
                    );
                  }
                });
              }
            });
          } else {
            Object.keys(inverterData).forEach(key => {
              const item = inverterData[key];
              if (!combined[key]) {
                combined[key] = {
                  color: item.color,
                  kml: [],
                  properties: item.properties || {},
                  is_polyline: 'is_polyline' in item,
                };
              }
              if (item.kml) {
                const kmlItems = Array.isArray(item.kml) ? item.kml : [item.kml];
                combined[key].kml.push(...kmlItems.map(kml => `${inverterName}/${kml}`));
              }
            });
          }
        }
      });
      return combined;
    },
  },
  methods: {
    selectAll() {
      this.select.selected = [...this.select.options];
    },
    clearAll() {
      this.select.selected = [];
    },
    showInfo() {
      this.$bvModal.show('pop-carousel');
    },
    rgbToHex(r, g, b) {
      return `#${((r << 16) | (g << 8) | b).toString(16).padStart(6, '0')}`;
    },
    renderDetails(keyL2, keyL3, value) {
      if (!this.isbetaSCQM) {
        this.clickedDetailsVisible = true;
      }
      this.$root.$emit('removeKml');
      this.clickedSummary = keyL2 + keyL3;
      this.clickedDetails = value;
      this.clickedSubLink = value;
      this.kmlheading = `${keyL3} (${this.select.selected.length} inverters)`;
      this.total_percentage =
        this.clickedDetails.Total == 'NA'
          ? 0
          : (this.clickedDetails.Actual / this.clickedDetails.Total) * 100;
      let hex_color;
      if (this.isbetaSCQM && value.kml) {
        const rgb = value.color
          .replace(/[^\d,]/g, '')
          .split(',')
          .map(Number);
        hex_color = `#${rgb.map(c => c.toString(16).padStart(2, '0')).join('')}`;
        const kmlItems = Array.isArray(value.kml) ? value.kml : [value.kml];
        kmlItems.forEach(kml => {
          this.$root.$emit('kmlClicked', {
            url: `${this.kmlBaseUrl}GLOBAL/inverter/${kml}`,
            hex: hex_color,
            is_polyline: kml.is_polyline == true,
          });
        });
      } else {
        hex_color = this.rgbToHex(value.color[0], value.color[1], value.color[2]);
        const kmlItems = Array.isArray(value.kml) ? value.kml : [value.kml];
        kmlItems.forEach(kml => {
          const kml_url = kml.includes('.kml')
            ? `${this.kmlBaseUrl}GLOBAL/inverter/${kml}`
            : `${this.kmlBaseUrl}GLOBAL/inverter/${kml}.kml`;
          this.$root.$emit('kmlClicked', {
            url: kml_url,
            hex: hex_color,
            is_polyline: kml.is_polyline == true,
          });
        });
      }
    },
    closeDetails() {
      this.clickedDetails = {};
      this.clickedDetailsVisible = false;
      this.$root.$emit('removeKml');
    },
    inverterPageChange(selectedInverters) {
      this.inverterCurrentPage =
        selectedInverters.length > 0 ? selectedInverters[0] : this.data.inverters[0];
      this.clickedDetails = {};
      this.clickedDetailsVisible = false;
      this.$root.$emit('removeKml');
    },
  },
  unmounted() {
    this.$root.$emit('removeKml');
  },
};
</script>

<style scoped>
.active {
  color: var(--primary) !important;
}
</style>
