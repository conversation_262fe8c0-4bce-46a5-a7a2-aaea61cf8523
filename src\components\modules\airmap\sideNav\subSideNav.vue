<template>
  <b-sidebar
    id="sidebar"
    :title="sideBarHeader"
    :visible="sideBarVisible"
    shadow="lg"
    lazy
    no-header-close>
    <div class="sideBarContainer">
      <component v-if="currentComponent" :is="currentComponent" :data="currentComponentData" />
    </div>
  </b-sidebar>
</template>

<script>
const activitySideNav = () => import('./activitySideNav.vue');
const aoiSideNav = () => import('./aoiSideNav.vue');
const cadAlignSideNav = () => import('./cadAlignSideNav.vue');
const assetsSideNav = () => import('./assetsSideNav.vue');

const summarySideNav = () => import('./summarySideNav.vue');
const inverterSideNav = () => import('./inverterSideNav.vue');
const deviation = () => import('./deviation.vue');
const inverterdeviation = () => import('./inverterdeviation.vue');

import { mapGetters } from 'vuex';

export default {
  name: 'subSideNav',
  components: {
    summarySideNav,
    assetsSideNav,
    inverterSideNav,
    aoiSideNav,
    cadAlignSideNav,
    activitySideNav,
    deviation,
    inverterdeviation,
  },
  props: {
    sideBarVisible: Boolean,
    sideBarHeader: String,
  },
  computed: {
    ...mapGetters({
      summaryData: 'homeStore/getSummaryData',
      inverterData: 'homeStore/getInverterData',
      activityData: 'airmapStore/getActivityArray',
      aoiData: 'airmapStore/getAoiArray',
      cadData: 'airmapStore/getCadArray',
    }),
    componentMapping() {
      return {
        Summary: 'summarySideNav',
        Assets: 'assetsSideNav',
        Deviation: 'deviation',
        'Sub Camp': 'inverterSideNav',
        'Sub Camp Deviation': 'inverterdeviation',
        Activities: 'activitySideNav',
        'Area of Intrest': 'aoiSideNav',
        CAD: 'cadAlignSideNav',
      };
    },
    currentComponent() {
      return this.componentMapping[this.sideBarHeader] || null;
    },
    currentComponentData() {
      switch (this.sideBarHeader) {
        case 'Summary':
        case 'Deviation':
        case 'Assets':
          return this.summaryData;
        case 'Sub Camp':
        case 'Sub Camp Deviation':
          return this.inverterData;
        case 'Activities':
          return this.activityData;
        case 'Area of Intrest':
          return this.aoiData;
        case 'CAD':
          return this.cadData;
        default:
          return {};
      }
    },
  },
};
</script>

<style scoped>
.sideBarContainer {
  display: flex;
  flex-direction: column;
  height: 100%;
}
</style>
