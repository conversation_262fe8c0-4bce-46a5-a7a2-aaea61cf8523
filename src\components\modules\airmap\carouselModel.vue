<template>
  <div>
    <b-modal
      id="pop-carousel"
      size="lg"
      title="References"
      :hide-footer="true"
      centered
      no-close-on-esc
      no-close-on-backdrop>
      <b-carousel fade controls indicators>
        <b-carousel-slide v-for="(image, index) in projectImages" :key="index">
          <template #img>
            <div class="mx-5">
              <b-img :src="image" :alt="`Image slot ${index}`" rounded thumbnail fluid></b-img>
            </div>
          </template>
        </b-carousel-slide>
      </b-carousel>
    </b-modal>
  </div>
</template>

<script>
export default {
  name: 'carouselModel',
  computed: {
    projectImages() {
      return this.$store.state.homeStore.projectData.slide_show;
    },
  },
};
</script>

<style scoped></style>
