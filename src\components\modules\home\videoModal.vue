<template>
  <div>
    <b-tabs card fill justified active-nav-item-class="font-weight-bold text-primary">
      <b-tab active>
        <template #title>
          <i class="fas fa-camera-retro mr-2"></i>
          Current Field Status
        </template>
        <div v-if="field_status_video_url">
          <video
            ref="currentVideo"
            class="w-100"
            @timeupdate="updateTime('current')"
            @loadedmetadata="setVideoDuration('current')">
            <source :src="field_status_video_url" type="video/mp4" />
            Your browser does not support the video tag.
          </video>
          <div class="d-flex align-items-center justify-content-between mt-2">
            <button
              @click="togglePlayPause('current')"
              class="btn btn-primary"
              v-html="
                isPlaying.current ? '<i class=\'fa fa-pause\'></i>' : '<i class=\'fa fa-play\'></i>'
              "></button>
            <input
              type="range"
              min="0"
              :max="videoDuration.current"
              step="0.1"
              v-model="currentTime.current"
              @input="seekVideo('current')"
              class="vdo-input" />
            <span class="font-weight-bolder">
              {{ formatTime(currentTime.current) }} / {{ formatTime(videoDuration.current) }}
            </span>
          </div>
        </div>
        <div v-else class="text-center font-weight-bolder m-0" style="font-size: 24px">
          Video not available for the date selected.
        </div>
      </b-tab>
      <b-tab>
        <template #title>
          <i class="fas fa-film mr-2"></i>
          Time Lapse View
        </template>
        <div v-if="time_lapse_video_url">
          <video
            ref="timelapseVideo"
            class="w-100"
            @timeupdate="updateTime('timelapse')"
            @loadedmetadata="setVideoDuration('timelapse')">
            <source :src="time_lapse_video_url" type="video/mp4" />
            Your browser does not support the video tag.
          </video>
          <div class="d-flex align-items-center justify-content-between mt-2">
            <button
              @click="togglePlayPause('timelapse')"
              class="btn btn-primary"
              v-html="
                isPlaying.timelapse
                  ? '<i class=\'fa fa-pause\'></i>'
                  : '<i class=\'fa fa-play\'></i>'
              "></button>
            <input
              type="range"
              min="0"
              :max="videoDuration.timelapse"
              step="0.1"
              v-model="currentTime.timelapse"
              @input="seekVideo('timelapse')"
              class="vdo-input" />
            <span class="font-weight-bolder">
              {{ formatTime(currentTime.timelapse) }} / {{ formatTime(videoDuration.timelapse) }}
            </span>
          </div>
        </div>
        <div v-else class="text-center font-weight-bolder m-0" style="font-size: 24px">
          Video not available for the date selected.
        </div>
      </b-tab>
    </b-tabs>
  </div>
</template>

<script>
export default {
  name: 'videoModal',
  props: {
    field_status_video_url: String,
    time_lapse_video_url: String,
  },
  data() {
    return {
      isPlaying: {
        current: false,
        timelapse: false,
      },
      currentTime: {
        current: 0,
        timelapse: 0,
      },
      videoDuration: {
        current: 0,
        timelapse: 0,
      },
    };
  },
  methods: {
    togglePlayPause(type) {
      const video = this.$refs[type + 'Video'];
      if (!video) return;

      if (this.isPlaying[type]) {
        video.pause();
      } else {
        video.play().catch(err => console.error('Playback failed:', err));
      }
      this.isPlaying[type] = !this.isPlaying[type];
    },
    updateTime(type) {
      const video = this.$refs[type + 'Video'];
      if (video) this.currentTime[type] = video.currentTime;
    },
    seekVideo(type) {
      const video = this.$refs[type + 'Video'];
      if (video) video.currentTime = this.currentTime[type];
    },
    setVideoDuration(type) {
      const video = this.$refs[type + 'Video'];
      if (video) this.videoDuration[type] = video.duration;
    },
    formatTime(seconds) {
      const mins = Math.floor(seconds / 60);
      const secs = Math.floor(seconds % 60);
      return `${mins}:${secs < 10 ? '0' : ''}${secs}`;
    },
  },
};
</script>

<style scoped>
.vdo-input {
  flex-grow: 1;
  margin: 0 10px;
  accent-color: darkcyan;
  cursor: pointer;
}
</style>
