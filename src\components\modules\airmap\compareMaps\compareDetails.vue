<template>
  <div class="compareDetailsContainer">
    <div class="d-flex justify-content-end mr-3 my-1">
      <VueToggles
        @click="isChart = !isChart"
        :value="isChart"
        height="50"
        width="80"
        checkedText="Chart"
        uncheckedText="Table"
        checkedBg="darkcyan"
        uncheckedBg="darkcyan"
        fontSize="20px"
        fontWeight="500" />
    </div>
    <div v-if="isChart">
      <h6 class="text-center">
        Showing {{ this.start !== 0 ? this.start : 1 }} - {{ this.end }} of {{ totalAssets }} Assets
      </h6>
      <div>
        <b-row>
          <b-col cols="8">
            <b-pagination
              :total-rows="totalAssets"
              v-model="currentPage"
              :per-page="perPage"
              @change="paginate"
              first-text="First"
              prev-text="Prev"
              next-text="Next"
              last-text="Last"
              pills></b-pagination>
          </b-col>
          <b-col cols="2">
            <b-form-select class="w-50" v-model="perPage" :options="perPageOptions"></b-form-select>
          </b-col>
        </b-row>
      </div>
      <div class="chartContainer mx-auto">
        <apexchart type="bar" :options="options" :series="series"></apexchart>
      </div>
    </div>
    <div v-else class="details">
      <b-pagination
        :total-rows="tableData.length"
        v-model="currentPageTable"
        :per-page="perPageTable"
        @change="paginate"
        first-text="First"
        prev-text="Prev"
        next-text="Next"
        last-text="Last"
        pills></b-pagination>
      <b-table
        id="contact-table"
        :items="tableData"
        :fields="fields"
        :per-page="perPageTable"
        :current-page="currentPageTable"></b-table>
    </div>
  </div>
</template>

<script>
import { mapState } from 'vuex';
export default {
  name: 'compareDetails',
  data() {
    return {
      perPage: 5,
      currentPage: 1,
      perPageOptions: [5, 10, 15],
      perPageTable: 5,
      currentPageTable: 1,
      isChart: false,
    };
  },
  computed: {
    ...mapState({
      leftDate: state => state.homeStore.compareDataLeft.date,
      rightDate: state => state.homeStore.compareDataRight.date,
      summaryLeft: state => state.homeStore.compareDataLeft.summary,
      summaryRight: state => state.homeStore.compareDataRight.summary,
      projectData: state => state.homeStore.projectData,
    }),
    totalAssets() {
      let assetsNumber = Object.keys(this.summaryLeft).length;
      return assetsNumber ? assetsNumber : 0;
    },
    start() {
      let start = (this.currentPage - 1) * this.perPage;
      return start ? start : 0;
    },
    end() {
      let end = this.currentPage * this.perPage;
      if (end < this.totalAssets) return end ? end : 0;
      else return this.totalAssets;
    },
    shownAssets() {
      let allAssets = Object.keys(this.summaryLeft);
      let shownAssets = {};
      let iterator = this.projectData.entries();
      let values_left = [];
      let values_right = [];

      for (let e of iterator) {
        if ((e[0] = this.leftDate)) {
          if (e[0] == e[1].date) {
            let newObj = Object.values(e[1].summary);

            for (let ne of newObj) {
              values_left.push(ne.Area_m2);
            }
          }
        }
        if ((e[0] = this.rightDate)) {
          if (e[0] == e[1].date) {
            let newObj = Object.values(e[1].summary);
            for (let ne of newObj) {
              values_right.push(ne.Area_m2);
            }
          }
        }
      }
      shownAssets.names = allAssets.slice(this.start, this.end);
      shownAssets.leftData = values_left.slice(this.start, this.end);
      shownAssets.rightData = values_right.slice(this.start, this.end);
      return shownAssets;
    },
    options() {
      return {
        chart: {
          id: 'vuechart-compare',
        },
        xaxis: {
          categories: this.shownAssets.names,
          labels: {
            style: {
              fontSize: '12px',
              fontFamily: 'Helvetica, Arial, sans-serif',
              fontWeight: 600,
              cssClass: 'apexcharts-xaxis-label',
            },
          },
        },
        yaxis: {
          labels: {
            style: {
              fontSize: '12px',
              fontFamily: 'Helvetica, Arial, sans-serif',
              fontWeight: 600,
              cssClass: 'apexcharts-yaxis-label',
            },
          },
        },
        plotOptions: {
          bar: {
            horizontal: false,
            dataLabels: {
              position: 'bottom',
            },
          },
        },
        legend: {
          fontSize: '16px',
          fontFamily: 'Helvetica, Arial',
          fontWeight: 400,
          itemMargin: {
            horizontal: 10,
            vertical: 5,
          },
        },
        dataLabels: {
          enabled: true,
          enabledOnSeries: undefined,
          textAnchor: 'middle',
          distributed: false,
          offsetX: 0,
          offsetY: 0,
          style: {
            fontSize: '14px',
            fontFamily: 'Helvetica, Arial, sans-serif',
            fontWeight: 'bold',
            colors: ['#fff'],
          },
          background: {
            enabled: true,
            foreColor: '#000',
            padding: 4,
            borderRadius: 2,
            borderWidth: 1,
            borderColor: '#fff',
            opacity: 0.9,
            dropShadow: {
              enabled: false,
              top: 1,
              left: 1,
              blur: 1,
              color: '#000',
              opacity: 0.45,
            },
          },
          dropShadow: {
            enabled: false,
            top: 1,
            left: 1,
            blur: 1,
            color: '#000',
            opacity: 0.45,
          },
        },
        responsive: [
          {
            breakpoint: 995,
            xaxis: {
              labels: {
                style: {
                  fontSize: '10px',
                  fontFamily: 'Helvetica, Arial, sans-serif',
                  fontWeight: 400,
                  cssClass: 'apexcharts-xaxis-label',
                },
              },
            },
            yaxis: {
              labels: {
                style: {
                  fontSize: '10px',
                  fontFamily: 'Helvetica, Arial, sans-serif',
                  fontWeight: 400,
                  cssClass: 'apexcharts-yaxis-label',
                },
              },
            },
            options: {
              plotOptions: {
                bar: {
                  horizontal: true,
                },
              },
              legend: {
                position: 'right',
                verticalAlign: 'top',
                fontSize: '14px',
                fontWeight: 500,
                itemMargin: {
                  horizontal: 10,
                  vertical: 5,
                },
              },
              dataLabels: {
                enabled: false,
              },
            },
          },
        ],
      };
    },
    series() {
      return [
        {
          name: `${this.leftDate}`,
          data: this.shownAssets.leftData,
        },
        {
          name: `${this.rightDate}`,
          data: this.shownAssets.rightData,
        },
      ];
    },
    //Table data
    tableData() {
      return this.$store.getters['homeStore/getCompareDataTable'] ?? [];
    },
    fields() {
      const objectKeys = Object.keys(this.tableData[0] ? this.tableData[0] : {});
      let fieldData = objectKeys.map(key => {
        return { key: key, sortable: true };
      });
      fieldData = [
        { key: 'Asset', label: 'Asset', sortable: true },
        { key: 'Left Data', label: this.leftDate, sortable: true },
        { key: 'Right Data', label: this.rightDate, sortable: true },
      ];
      return fieldData ?? [];
    },
  },
  methods: {
    paginate(page) {
      this.currentPage = page;
    },
  },
};
</script>

<style scoped>
.compareDetailsContainer {
  display: flex;
  flex-direction: column;
  width: 100%;
}
</style>
