<template>
  <div class="inner_login">
    <b-card class="shadow mx-auto w-100" style="max-width: 500px">
      <div class="text-center text-primary">
        <i class="fa fa-unlock-alt fa-4x"></i>
        <p class="my-2 h4 font-weight-bold">Account Recovery</p>
      </div>
      <b-form class="w-100">
        <b-form-group class="my-4">
          <b-form-input
            class="w-100"
            id="email-input"
            v-model="form.email"
            type="email"
            placeholder="Enter your registered email"
            @blur="v$.form.email.$touch()"></b-form-input>
          <b-form-invalid-feedback :state="validateState('email')">
            Enter a valid registered email
          </b-form-invalid-feedback>
        </b-form-group>
        <div class="d-flex justify-content-center align-items-center">
          <b-button class="primaryBg mr-4" @click="onSubmit()" :disabled="v$.form.$invalid">
            Submit
          </b-button>
          <p class="m-0 font-weight-bold">Back to <router-link to="/login"> Login</router-link></p>
        </div>
      </b-form>
    </b-card>
  </div>
</template>

<script>
import { useVuelidate } from '@vuelidate/core';
import { email, required } from '@vuelidate/validators';

export default {
  name: 'accountRecovery',
  setup() {
    return { v$: useVuelidate() };
  },
  data() {
    return {
      form: {
        email: '',
      },
    };
  },
  validations: {
    form: {
      email: {
        email,
        required,
      },
    },
  },
  methods: {
    validateState(name) {
      const field = this.v$.form[name];
      return field.$invalid && field.$dirty ? false : null;
    },
    onSubmit() {
      this.$store
        .dispatch('userStore/generateLink', this.form)
        .then(response => {
          this.form.email = '';
          this.$root.$emit('showToast', {
            message: 'Email Sent',
            title: response.message,
            variant: 'success',
          });
        })
        .catch(error => {
          this.$root.$emit('showToast', {
            message: `Try again later or contact support`,
            title: error.message,
            variant: 'danger',
          });
        });
    },
  },
};
</script>

<style scoped></style>
