<template>
  <div>
    <div class="row justify-content-center text-center">
      <div class="col-12 col-md-6 mb-md-0">
        <b-form-group>
          <b-input-group>
            <b-form-input v-model="searchQuery" placeholder="Search..." />
            <b-input-group-append>
              <b-button :disabled="!searchQuery" @click="clearSearch">Clear</b-button>
            </b-input-group-append>
          </b-input-group>
        </b-form-group>
      </div>
      <div
        class="col-12 col-md-auto d-flex align-items-center justify-content-center font-weight-bold">
        <p>Showing {{ filteredItems.length }} of {{ accessList.length }} rows</p>
      </div>
    </div>
    <b-table
      bordered
      striped
      hover
      responsive="sm"
      show-empty
      sticky-header
      no-border-collapse
      head-variant="dark"
      :items="paginatedItems"
      :fields="fields"
      @sort-changed="handleSort">
      <template #emptyfiltered>
        <h6><b>Search data not found.</b></h6>
      </template>
      <template #cell(name)="data">
        <b>{{ data.value }}</b>
      </template>
      <template #cell(role)="data">
        <span class="text-capitalize">{{ data.value }}</span>
      </template>
      <template #cell(access)="data">
        <div class="d-flex justify-content-center">
          <VueToggles
            @click="changeAccess(data.item)"
            :value="data.item.access"
            height="25"
            checkedText="Yes"
            uncheckedText="No"
            checkedBg="darkcyan"
            uncheckedBg="#343a40"
            fontSize="14"
            fontWeight="800" />
        </div>
      </template>
    </b-table>
    <b-pagination
      v-if="filteredItems.length > itemsPerPage"
      v-model="currentPage"
      :total-rows="filteredItems.length"
      :per-page="itemsPerPage"
      first-text="First"
      prev-text="Prev"
      next-text="Next"
      last-text="Last"
      align="center"
      pills
      :limit="2"
      class="mt-3" />
  </div>
</template>

<script>
export default {
  name: 'shareModal',
  props: ['projectData'],
  data() {
    return {
      fields: [
        { key: 'name', sortable: true },
        { key: 'email', sortable: true },
        { key: 'role', sortable: true },
        { key: 'access', sortable: true },
      ],
      accessList: [],
      searchQuery: '',
      sortBy: '',
      sortDesc: false,
      currentPage: 1,
      itemsPerPage: 5,
    };
  },
  computed: {
    manageUsers() {
      return this.$store.state.userStore.manageUsers;
    },
    filteredItems() {
      let filtered = this.accessList;
      // Apply search filter across all fields
      if (this.searchQuery) {
        const search = this.searchQuery.toLowerCase().trim();
        filtered = filtered.filter(item =>
          Object.values(item).some(value => value?.toString().toLowerCase().includes(search))
        );
      }
      // Apply sorting
      if (this.sortBy) {
        const isDescending = this.sortDesc;
        filtered = filtered.slice().sort((a, b) => {
          const aVal = a[this.sortBy] ?? '';
          const bVal = b[this.sortBy] ?? '';
          const aStr = typeof aVal === 'string' ? aVal.toLowerCase() : String(aVal).toLowerCase();
          const bStr = typeof bVal === 'string' ? bVal.toLowerCase() : String(bVal).toLowerCase();
          return isDescending
            ? bStr.localeCompare(aStr, undefined, { numeric: true })
            : aStr.localeCompare(bStr, undefined, { numeric: true });
        });
      }
      return filtered;
    },
    paginatedItems() {
      const start = (this.currentPage - 1) * this.itemsPerPage;
      const end = start + this.itemsPerPage;
      return this.filteredItems.slice(start, end);
    },
  },
  methods: {
    clearSearch() {
      this.searchQuery = '';
      this.currentPage = 1;
    },
    handleSort(ctx) {
      this.sortBy = ctx.sortBy;
      this.sortDesc = ctx.sortDesc;
    },
    changeAccess(data) {
      const requestData = {
        db_id: data.db_id,
        project_id: this.projectData.id,
        access: !data.access,
      };
      this.$store
        .dispatch('homeStore/userProjectAccess', requestData)
        .then(async response => {
          this.$root.$emit('showToast', {
            title: response.message,
            message: 'User access updated successfully',
            variant: 'success',
          });
          await this.$store.dispatch('homeStore/getAllProjects', true);
          this.$bvModal.hide('shareModal');
        })
        .catch(error => {
          this.$root.$emit('showToast', {
            title: error.message,
            message: 'There has been a problem in updating data',
            variant: 'danger',
          });
        });
    },
    getAccessList() {
      const usersWithAccessSet = new Set(this.projectData.users.map(user => user.id));
      this.accessList = this.manageUsers.map(user => {
        return {
          ...user,
          access: usersWithAccessSet.has(user.db_id),
        };
      });
    },
  },
  mounted() {
    this.getAccessList();
  },
  watch: {
    searchQuery() {
      this.currentPage = 1;
    },
  },
};
</script>

<style scoped></style>
